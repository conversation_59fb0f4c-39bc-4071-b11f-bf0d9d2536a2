import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Search, Calendar, Clock, Upload, Check, X, FileText, HelpCircle } from "lucide-react";
import { toast } from "sonner";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

const SkillGapAnalyzer = () => {
  const [query, setQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [jobTitle, setJobTitle] = useState("");
  const [jobDescription, setJobDescription] = useState("");
  const [jobDescriptionFile, setJobDescriptionFile] = useState<File | null>(null);
  const [skillAnalysisMode, setSkillAnalysisMode] = useState<"query" | "upload">("query");
  const [skillComparison, setSkillComparison] = useState<{
    matching: string[];
    partial: string[];
    missing: string[];
  } | null>(null);
  
  // Keep existing skillPlan state
  const [skillPlan, setSkillPlan] = useState<{
    targetSkill: string;
    timeframe: string;
    currentLevel: number;
    targetLevel: number;
    steps: Array<{
      title: string;
      description: string;
      duration: string;
      resources: Array<{
        type: string;
        title: string;
        link: string;
        duration?: string;
      }>;
    }>;
  } | null>(null);

  const handleResumeUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    if (file) {
      setResumeFile(file);
      toast.success(`Resume uploaded: ${file.name}`);
    }
  };

  const handleJobDescriptionUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    if (file) {
      setJobDescriptionFile(file);
      toast.success(`Job description uploaded: ${file.name}`);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (skillAnalysisMode === "query") {
      if (!query.trim()) {
        toast.error("Please enter a skill gap query");
        return;
      }
      
      setIsLoading(true);
      
      try {
        // This would be replaced with an actual LLM API call
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Mock data - would be generated by LLM
        setSkillPlan({
          targetSkill: "Generative AI",
          timeframe: "2 weeks",
          currentLevel: 1,
          targetLevel: 7,
          steps: [
            {
              title: "Foundation Knowledge",
              description: "Learn the basic concepts and principles of generative AI models including transformers, diffusion models, and GANs.",
              duration: "3 days",
              resources: [
                {
                  type: "Course",
                  title: "Introduction to Generative AI",
                  link: "https://example.com/course",
                  duration: "4 hours"
                },
                {
                  type: "Article",
                  title: "Understanding Different Types of Generative Models",
                  link: "https://example.com/article",
                  duration: "30 minutes"
                },
                {
                  type: "Video",
                  title: "How Generative AI Works",
                  link: "https://example.com/video",
                  duration: "45 minutes"
                }
              ]
            },
            {
              title: "Practical Applications",
              description: "Learn how to use popular generative AI tools and platforms for practical business applications.",
              duration: "4 days",
              resources: [
                {
                  type: "Workshop",
                  title: "Hands-on with GPT Models",
                  link: "https://example.com/workshop",
                  duration: "3 hours"
                },
                {
                  type: "Tutorial",
                  title: "Building Applications with Generative AI APIs",
                  link: "https://example.com/tutorial",
                  duration: "2 hours"
                },
                {
                  type: "Project",
                  title: "Create a Simple AI Content Generator",
                  link: "https://example.com/project",
                  duration: "8 hours"
                }
              ]
            },
            {
              title: "Business Implementation",
              description: "Learn how to integrate generative AI into business workflows and product development processes.",
              duration: "5 days",
              resources: [
                {
                  type: "Case Study",
                  title: "How Top Companies Use Generative AI",
                  link: "https://example.com/casestudy",
                  duration: "1 hour"
                },
                {
                  type: "Course",
                  title: "Generative AI for Product Managers",
                  link: "https://example.com/course2",
                  duration: "6 hours"
                },
                {
                  type: "Project",
                  title: "Design a Generative AI Integration Strategy",
                  link: "https://example.com/project2",
                  duration: "10 hours"
                }
              ]
            },
            {
              title: "Ethical Considerations",
              description: "Understand the ethical implications and best practices for responsible generative AI use.",
              duration: "2 days",
              resources: [
                {
                  type: "Webinar",
                  title: "Ethics in Generative AI",
                  link: "https://example.com/webinar",
                  duration: "90 minutes"
                },
                {
                  type: "Guide",
                  title: "Responsible AI Implementation Framework",
                  link: "https://example.com/guide",
                  duration: "2 hours"
                }
              ]
            }
          ]
        });
        
        setSkillComparison(null);
        toast.success("Skill plan generated!");
      } catch (error) {
        toast.error("Failed to generate skill plan. Please try again.");
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    } else {
      // Resume and job upload analysis mode
      if (!resumeFile) {
        toast.error("Please upload your resume");
        return;
      }
      
      if (!jobTitle && !jobDescription && !jobDescriptionFile) {
        toast.error("Please enter a job title or upload a job description");
        return;
      }
      
      setIsLoading(true);
      
      try {
        // Mock API call - would be replaced with real backend integration later
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Mock analysis results
        const mockComparison = {
          matching: ["React", "TypeScript", "Redux", "Jest", "HTML/CSS", "Git"],
          partial: ["Node.js", "Docker", "Agile Methodologies"],
          missing: ["AWS", "GraphQL", "CI/CD", "Kubernetes", "MongoDB"]
        };
        
        setSkillPlan(null);
        setSkillComparison(mockComparison);
        toast.success("Skill analysis complete!");
      } catch (error) {
        toast.error("Failed to analyze skills. Please try again.");
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const resetAnalysis = () => {
    setResumeFile(null);
    setJobTitle("");
    setJobDescription("");
    setJobDescriptionFile(null);
    setSkillComparison(null);
  };

  return (
    <div className="space-y-6">
      <div className="bg-muted/40 p-4 rounded-lg">
        <div className="flex gap-4 mb-4">
          <Button 
            variant={skillAnalysisMode === "query" ? "default" : "outline"}
            onClick={() => setSkillAnalysisMode("query")}
          >
            Quick Query
          </Button>
          <Button 
            variant={skillAnalysisMode === "upload" ? "default" : "outline"}
            onClick={() => {
              setSkillAnalysisMode("upload");
              setSkillPlan(null);
            }}
          >
            Resume Analysis
          </Button>
        </div>

        {skillAnalysisMode === "query" ? (
          <form onSubmit={handleSubmit} className="flex gap-2">
            <Input
              placeholder="e.g., My profile lacks 'Generative AI' skills—need a 2-week upskilling plan"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="flex-1"
            />
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Analyzing..." : <Search className="mr-2" />}
              {isLoading ? "" : "Analyze"}
            </Button>
          </form>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <div className="flex justify-between items-center mb-2">
                <Label htmlFor="resume" className="text-base font-medium flex items-center gap-2">
                  Upload Your Resume
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle size={16} className="text-muted-foreground cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">Upload your resume in PDF, DOCX, or TXT format.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                {resumeFile && (
                  <span className="text-sm text-muted-foreground">{resumeFile.name}</span>
                )}
              </div>
              <div className="border-2 border-dashed rounded-lg p-6 text-center bg-muted/50 hover:bg-muted/80 transition-colors cursor-pointer relative">
                <input
                  id="resume"
                  type="file"
                  accept=".pdf,.docx,.txt"
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  onChange={handleResumeUpload}
                />
                <div className="flex flex-col items-center justify-center space-y-2">
                  <Upload className="h-10 w-10 text-muted-foreground" />
                  <p className="text-sm font-medium">
                    {resumeFile ? "Replace resume file" : "Drag & drop or click to upload"}
                  </p>
                  <p className="text-xs text-muted-foreground">PDF, DOCX or TXT (max 5MB)</p>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="jobTitle" className="text-base font-medium">
                Job Title
              </Label>
              <Input
                id="jobTitle"
                placeholder="e.g., Senior React Developer"
                value={jobTitle}
                onChange={(e) => setJobTitle(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="jobDescription" className="text-base font-medium">
                  Job Description
                </Label>
                <span className="text-xs text-muted-foreground">Or upload a file</span>
              </div>
              <Textarea
                id="jobDescription"
                placeholder="Paste the job description here..."
                value={jobDescription}
                onChange={(e) => setJobDescription(e.target.value)}
                rows={5}
              />
              <div className="flex items-center mt-2">
                <div className="relative">
                  <input
                    id="jobDescriptionFile"
                    type="file"
                    accept=".pdf,.docx,.txt"
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    onChange={handleJobDescriptionUpload}
                  />
                  <Button type="button" variant="outline" size="sm">
                    <FileText className="mr-2" size={16} />
                    Upload Job Description
                  </Button>
                </div>
                {jobDescriptionFile && (
                  <span className="ml-2 text-sm text-muted-foreground">
                    {jobDescriptionFile.name}
                  </span>
                )}
              </div>
            </div>

            <Button type="submit" disabled={isLoading} className="w-full">
              {isLoading ? "Analyzing Skills..." : "Analyze My Skills"}
            </Button>
          </form>
        )}
      </div>

      {/* Display Skill Comparison Results */}
      {skillComparison && (
        <div className="mt-8 space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-bold">Skill Gap Analysis Results</h3>
            <Button variant="outline" size="sm" onClick={resetAnalysis}>
              New Analysis
            </Button>
          </div>

          <div className="grid md:grid-cols-3 gap-4">
            {/* Matching Skills */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center gap-2 mb-4">
                <div className="p-1.5 rounded-full bg-green-100">
                  <Check size={18} className="text-green-600" />
                </div>
                <h4 className="font-semibold">Matching Skills</h4>
              </div>
              <ul className="space-y-2">
                {skillComparison.matching.map((skill, index) => (
                  <li key={index} className="flex items-center gap-2 p-2 bg-green-50 rounded">
                    <Check size={16} className="text-green-600 shrink-0" />
                    <span>{skill}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Partial Skills */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center gap-2 mb-4">
                <div className="p-1.5 rounded-full bg-amber-100">
                  <div className="h-[18px] w-[18px] rounded-full bg-amber-500" />
                </div>
                <h4 className="font-semibold">Partial Matches</h4>
              </div>
              <ul className="space-y-2">
                {skillComparison.partial.map((skill, index) => (
                  <li key={index} className="flex items-center gap-2 p-2 bg-amber-50 rounded">
                    <div className="h-4 w-4 rounded-full bg-amber-500 shrink-0" />
                    <span>{skill}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Missing Skills */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center gap-2 mb-4">
                <div className="p-1.5 rounded-full bg-red-100">
                  <X size={18} className="text-red-600" />
                </div>
                <h4 className="font-semibold">Missing Skills</h4>
              </div>
              <ul className="space-y-2">
                {skillComparison.missing.map((skill, index) => (
                  <li key={index} className="flex items-center gap-2 p-2 bg-red-50 rounded">
                    <X size={16} className="text-red-600 shrink-0" />
                    <span>{skill}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Display the original Skill Plan */}
      {skillPlan && (
        <div className="mt-6 space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold">{skillPlan.targetSkill} Upskilling Plan</h3>
              <p className="text-muted-foreground flex items-center gap-1 mt-1">
                <Calendar size={16} />
                {skillPlan.timeframe} timeframe
              </p>
            </div>
            <div className="bg-muted p-4 rounded-lg text-center">
              <div className="text-2xl font-bold">{skillPlan.currentLevel} → {skillPlan.targetLevel}</div>
              <div className="text-xs text-muted-foreground">Skill Level</div>
            </div>
          </div>
          
          <div className="space-y-6">
            {skillPlan.steps.map((step, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <h4 className="text-lg font-semibold">
                    {index + 1}. {step.title}
                  </h4>
                  <span className="bg-primary/10 text-primary text-xs px-2 py-1 rounded-full flex items-center gap-1">
                    <Clock size={12} />
                    {step.duration}
                  </span>
                </div>
                <p className="text-muted-foreground mt-2">{step.description}</p>
                
                <div className="mt-4 space-y-3">
                  <h5 className="font-medium text-sm">Recommended Resources:</h5>
                  <div className="grid gap-3 sm:grid-cols-2">
                    {step.resources.map((resource, i) => (
                      <div key={i} className="border rounded-md p-3 bg-card">
                        <div className="flex justify-between">
                          <span className="text-xs bg-secondary px-2 py-0.5 rounded-full">
                            {resource.type}
                          </span>
                          {resource.duration && (
                            <span className="text-xs text-muted-foreground">
                              {resource.duration}
                            </span>
                          )}
                        </div>
                        <h6 className="font-medium mt-1">{resource.title}</h6>
                        <a 
                          href={resource.link} 
                          className="text-xs text-primary hover:underline mt-1 block"
                          target="_blank" 
                          rel="noopener noreferrer"
                        >
                          View Resource
                        </a>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SkillGapAnalyzer;
