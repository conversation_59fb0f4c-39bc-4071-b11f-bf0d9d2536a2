# 📖 CareerBloom API Reference

## Base URL
```
http://localhost:3001/api
```

## 🎯 Resume APIs

### Resume Builder
**Generate Resume Content**
```http
POST /resume-builder/generate
Content-Type: application/json

{
  "personalInfo": {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "+1234567890"
  },
  "experience": [...],
  "skills": ["React", "Node.js"],
  "template": "modern"
}
```

### Resume Customizer
**Customize Resume for Job**
```http
POST /resume-customization/customize-text
Content-Type: application/json

{
  "resumeText": "Complete resume text...",
  "jobDescription": "Target job description..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "originalResume": "...",
    "customizedResume": "...",
    "newSummary": "AI-generated summary",
    "addedSkills": ["React", "Node.js"]
  }
}
```

## 🔍 Job Recommendation APIs

### Get Recommendations
```http
POST /job-recommendations/recommendations
Content-Type: application/json

{
  "resumeText": "Your resume content...",
  "limit": 10,
  "filters": {
    "location": "Remote",
    "salaryMin": 70000
  }
}
```

### Add Jobs to Database
```http
POST /job-recommendations/add-jobs
Content-Type: application/json

{
  "jobs": [
    {
      "title": "Senior Developer",
      "company": "TechCorp",
      "description": "Build scalable applications...",
      "skills": ["React", "TypeScript"],
      "salary": { "min": 90000, "max": 120000 },
      "location": "San Francisco, CA",
      "remote": true
    }
  ]
}
```

## 🛤️ Career Path APIs

### Generate Career Roadmap
```http
POST /career-path/generate
Content-Type: application/json

{
  "currentRole": "Junior Developer",
  "targetRole": "Senior Full Stack Developer",
  "timeframe": "2 years",
  "skills": ["JavaScript", "React"]
}
```

## 📊 Skills Assessment APIs

### Start Assessment
```http
POST /skills-assessment/start
Content-Type: application/json

{
  "category": "frontend",
  "difficulty": "intermediate",
  "questionCount": 15
}
```

### Submit Assessment
```http
POST /skills-assessment/submit
Content-Type: application/json

{
  "assessmentId": "uuid",
  "answers": [
    { "questionId": 1, "selectedOption": "A" },
    { "questionId": 2, "selectedOption": "C" }
  ]
}
```

## 📈 Job Trends APIs

### Get Market Trends
```http
GET /job-trends/analytics?location=remote&role=developer
```

### Get Salary Insights
```http
POST /job-trends/salary-analysis
Content-Type: application/json

{
  "role": "Software Engineer",
  "location": "San Francisco",
  "experience": "3-5 years"
}
```

## 💬 Chatbot APIs

### Send Message
```http
POST /chatbot/message
Content-Type: application/json

{
  "message": "How can I improve my resume?",
  "context": {
    "resumeData": "...",
    "jobData": "..."
  }
}
```

## 🔧 Utility APIs

### Health Check
```http
GET /health
```

### File Upload
```http
POST /upload/resume
Content-Type: multipart/form-data

resume: [file]
```

## 📝 Response Format

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "details": "Detailed error information"
}
```

## 🔑 Authentication

Some endpoints require authentication:
```http
Authorization: Bearer <your-jwt-token>
```

## 📊 Rate Limiting

- **General APIs**: 100 requests/minute
- **AI APIs**: 20 requests/minute
- **File Upload**: 10 requests/minute

## 🚨 Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input |
| 401 | Unauthorized - Missing/invalid auth |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server issue |
