{"compilerOptions": {"target": "es2017", "module": "commonjs", "lib": ["es6", "dom"], "allowJs": true, "outDir": "dist", "rootDir": ".", "strict": true, "noImplicitAny": true, "esModuleInterop": true, "resolveJsonModule": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["./*"]}, "typeRoots": ["./node_modules/@types", "./types"]}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist"]}