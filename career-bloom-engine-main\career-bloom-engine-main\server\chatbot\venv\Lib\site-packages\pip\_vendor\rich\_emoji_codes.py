EMOJI = {
    "1st_place_medal": "🥇",
    "2nd_place_medal": "🥈",
    "3rd_place_medal": "🥉",
    "ab_button_(blood_type)": "🆎",
    "atm_sign": "🏧",
    "a_button_(blood_type)": "🅰",
    "afghanistan": "🇦🇫",
    "albania": "🇦🇱",
    "algeria": "🇩🇿",
    "american_samoa": "🇦🇸",
    "andorra": "🇦🇩",
    "angola": "🇦🇴",
    "anguilla": "🇦🇮",
    "antarctica": "🇦🇶",
    "antigua_&_barbuda": "🇦🇬",
    "aquarius": "♒",
    "argentina": "🇦🇷",
    "aries": "♈",
    "armenia": "🇦🇲",
    "aruba": "🇦🇼",
    "ascension_island": "🇦🇨",
    "australia": "🇦🇺",
    "austria": "🇦🇹",
    "azerbaijan": "🇦🇿",
    "back_arrow": "🔙",
    "b_button_(blood_type)": "🅱",
    "bahamas": "🇧🇸",
    "bahrain": "🇧🇭",
    "bangladesh": "🇧🇩",
    "barbados": "🇧🇧",
    "belarus": "🇧🇾",
    "belgium": "🇧🇪",
    "belize": "🇧🇿",
    "benin": "🇧🇯",
    "bermuda": "🇧🇲",
    "bhutan": "🇧🇹",
    "bolivia": "🇧🇴",
    "bosnia_&_herzegovina": "🇧🇦",
    "botswana": "🇧🇼",
    "bouvet_island": "🇧🇻",
    "brazil": "🇧🇷",
    "british_indian_ocean_territory": "🇮🇴",
    "british_virgin_islands": "🇻🇬",
    "brunei": "🇧🇳",
    "bulgaria": "🇧🇬",
    "burkina_faso": "🇧🇫",
    "burundi": "🇧🇮",
    "cl_button": "🆑",
    "cool_button": "🆒",
    "cambodia": "🇰🇭",
    "cameroon": "🇨🇲",
    "canada": "🇨🇦",
    "canary_islands": "🇮🇨",
    "cancer": "♋",
    "cape_verde": "🇨🇻",
    "capricorn": "♑",
    "caribbean_netherlands": "🇧🇶",
    "cayman_islands": "🇰🇾",
    "central_african_republic": "🇨🇫",
    "ceuta_&_melilla": "🇪🇦",
    "chad": "🇹🇩",
    "chile": "🇨🇱",
    "china": "🇨🇳",
    "christmas_island": "🇨🇽",
    "christmas_tree": "🎄",
    "clipperton_island": "🇨🇵",
    "cocos_(keeling)_islands": "🇨🇨",
    "colombia": "🇨🇴",
    "comoros": "🇰🇲",
    "congo_-_brazzaville": "🇨🇬",
    "congo_-_kinshasa": "🇨🇩",
    "cook_islands": "🇨🇰",
    "costa_rica": "🇨🇷",
    "croatia": "🇭🇷",
    "cuba": "🇨🇺",
    "curaçao": "🇨🇼",
    "cyprus": "🇨🇾",
    "czechia": "🇨🇿",
    "côte_d’ivoire": "🇨🇮",
    "denmark": "🇩🇰",
    "diego_garcia": "🇩🇬",
    "djibouti": "🇩🇯",
    "dominica": "🇩🇲",
    "dominican_republic": "🇩🇴",
    "end_arrow": "🔚",
    "ecuador": "🇪🇨",
    "egypt": "🇪🇬",
    "el_salvador": "🇸🇻",
    "england": "🏴\U000e0067\U000e0062\U000e0065\U000e006e\U000e0067\U000e007f",
    "equatorial_guinea": "🇬🇶",
    "eritrea": "🇪🇷",
    "estonia": "🇪🇪",
    "ethiopia": "🇪🇹",
    "european_union": "🇪🇺",
    "free_button": "🆓",
    "falkland_islands": "🇫🇰",
    "faroe_islands": "🇫🇴",
    "fiji": "🇫🇯",
    "finland": "🇫🇮",
    "france": "🇫🇷",
    "french_guiana": "🇬🇫",
    "french_polynesia": "🇵🇫",
    "french_southern_territories": "🇹🇫",
    "gabon": "🇬🇦",
    "gambia": "🇬🇲",
    "gemini": "♊",
    "georgia": "🇬🇪",
    "germany": "🇩🇪",
    "ghana": "🇬🇭",
    "gibraltar": "🇬🇮",
    "greece": "🇬🇷",
    "greenland": "🇬🇱",
    "grenada": "🇬🇩",
    "guadeloupe": "🇬🇵",
    "guam": "🇬🇺",
    "guatemala": "🇬🇹",
    "guernsey": "🇬🇬",
    "guinea": "🇬🇳",
    "guinea-bissau": "🇬🇼",
    "guyana": "🇬🇾",
    "haiti": "🇭🇹",
    "heard_&_mcdonald_islands": "🇭🇲",
    "honduras": "🇭🇳",
    "hong_kong_sar_china": "🇭🇰",
    "hungary": "🇭🇺",
    "id_button": "🆔",
    "iceland": "🇮🇸",
    "india": "🇮🇳",
    "indonesia": "🇮🇩",
    "iran": "🇮🇷",
    "iraq": "🇮🇶",
    "ireland": "🇮🇪",
    "isle_of_man": "🇮🇲",
    "israel": "🇮🇱",
    "italy": "🇮🇹",
    "jamaica": "🇯🇲",
    "japan": "🗾",
    "japanese_acceptable_button": "🉑",
    "japanese_application_button": "🈸",
    "japanese_bargain_button": "🉐",
    "japanese_castle": "🏯",
    "japanese_congratulations_button": "㊗",
    "japanese_discount_button": "🈹",
    "japanese_dolls": "🎎",
    "japanese_free_of_charge_button": "🈚",
    "japanese_here_button": "🈁",
    "japanese_monthly_amount_button": "🈷",
    "japanese_no_vacancy_button": "🈵",
    "japanese_not_free_of_charge_button": "🈶",
    "japanese_open_for_business_button": "🈺",
    "japanese_passing_grade_button": "🈴",
    "japanese_post_office": "🏣",
    "japanese_prohibited_button": "🈲",
    "japanese_reserved_button": "🈯",
    "japanese_secret_button": "㊙",
    "japanese_service_charge_button": "🈂",
    "japanese_symbol_for_beginner": "🔰",
    "japanese_vacancy_button": "🈳",
    "jersey": "🇯🇪",
    "jordan": "🇯🇴",
    "kazakhstan": "🇰🇿",
    "kenya": "🇰🇪",
    "kiribati": "🇰🇮",
    "kosovo": "🇽🇰",
    "kuwait": "🇰🇼",
    "kyrgyzstan": "🇰🇬",
    "laos": "🇱🇦",
    "latvia": "🇱🇻",
    "lebanon": "🇱🇧",
    "leo": "♌",
    "lesotho": "🇱🇸",
    "liberia": "🇱🇷",
    "libra": "♎",
    "libya": "🇱🇾",
    "liechtenstein": "🇱🇮",
    "lithuania": "🇱🇹",
    "luxembourg": "🇱🇺",
    "macau_sar_china": "🇲🇴",
    "macedonia": "🇲🇰",
    "madagascar": "🇲🇬",
    "malawi": "🇲🇼",
    "malaysia": "🇲🇾",
    "maldives": "🇲🇻",
    "mali": "🇲🇱",
    "malta": "🇲🇹",
    "marshall_islands": "🇲🇭",
    "martinique": "🇲🇶",
    "mauritania": "🇲🇷",
    "mauritius": "🇲🇺",
    "mayotte": "🇾🇹",
    "mexico": "🇲🇽",
    "micronesia": "🇫🇲",
    "moldova": "🇲🇩",
    "monaco": "🇲🇨",
    "mongolia": "🇲🇳",
    "montenegro": "🇲🇪",
    "montserrat": "🇲🇸",
    "morocco": "🇲🇦",
    "mozambique": "🇲🇿",
    "mrs._claus": "🤶",
    "mrs._claus_dark_skin_tone": "🤶🏿",
    "mrs._claus_light_skin_tone": "🤶🏻",
    "mrs._claus_medium-dark_skin_tone": "🤶🏾",
    "mrs._claus_medium-light_skin_tone": "🤶🏼",
    "mrs._claus_medium_skin_tone": "🤶🏽",
    "myanmar_(burma)": "🇲🇲",
    "new_button": "🆕",
    "ng_button": "🆖",
    "namibia": "🇳🇦",
    "nauru": "🇳🇷",
    "nepal": "🇳🇵",
    "netherlands": "🇳🇱",
    "new_caledonia": "🇳🇨",
    "new_zealand": "🇳🇿",
    "nicaragua": "🇳🇮",
    "niger": "🇳🇪",
    "nigeria": "🇳🇬",
    "niue": "🇳🇺",
    "norfolk_island": "🇳🇫",
    "north_korea": "🇰🇵",
    "northern_mariana_islands": "🇲🇵",
    "norway": "🇳🇴",
    "ok_button": "🆗",
    "ok_hand": "👌",
    "ok_hand_dark_skin_tone": "👌🏿",
    "ok_hand_light_skin_tone": "👌🏻",
    "ok_hand_medium-dark_skin_tone": "👌🏾",
    "ok_hand_medium-light_skin_tone": "👌🏼",
    "ok_hand_medium_skin_tone": "👌🏽",
    "on!_arrow": "🔛",
    "o_button_(blood_type)": "🅾",
    "oman": "🇴🇲",
    "ophiuchus": "⛎",
    "p_button": "🅿",
    "pakistan": "🇵🇰",
    "palau": "🇵🇼",
    "palestinian_territories": "🇵🇸",
    "panama": "🇵🇦",
    "papua_new_guinea": "🇵🇬",
    "paraguay": "🇵🇾",
    "peru": "🇵🇪",
    "philippines": "🇵🇭",
    "pisces": "♓",
    "pitcairn_islands": "🇵🇳",
    "poland": "🇵🇱",
    "portugal": "🇵🇹",
    "puerto_rico": "🇵🇷",
    "qatar": "🇶🇦",
    "romania": "🇷🇴",
    "russia": "🇷🇺",
    "rwanda": "🇷🇼",
    "réunion": "🇷🇪",
    "soon_arrow": "🔜",
    "sos_button": "🆘",
    "sagittarius": "♐",
    "samoa": "🇼🇸",
    "san_marino": "🇸🇲",
    "santa_claus": "🎅",
    "santa_claus_dark_skin_tone": "🎅🏿",
    "santa_claus_light_skin_tone": "🎅🏻",
    "santa_claus_medium-dark_skin_tone": "🎅🏾",
    "santa_claus_medium-light_skin_tone": "🎅🏼",
    "santa_claus_medium_skin_tone": "🎅🏽",
    "saudi_arabia": "🇸🇦",
    "scorpio": "♏",
    "scotland": "🏴\U000e0067\U000e0062\U000e0073\U000e0063\U000e0074\U000e007f",
    "senegal": "🇸🇳",
    "serbia": "🇷🇸",
    "seychelles": "🇸🇨",
    "sierra_leone": "🇸🇱",
    "singapore": "🇸🇬",
    "sint_maarten": "🇸🇽",
    "slovakia": "🇸🇰",
    "slovenia": "🇸🇮",
    "solomon_islands": "🇸🇧",
    "somalia": "🇸🇴",
    "south_africa": "🇿🇦",
    "south_georgia_&_south_sandwich_islands": "🇬🇸",
    "south_korea": "🇰🇷",
    "south_sudan": "🇸🇸",
    "spain": "🇪🇸",
    "sri_lanka": "🇱🇰",
    "st._barthélemy": "🇧🇱",
    "st._helena": "🇸🇭",
    "st._kitts_&_nevis": "🇰🇳",
    "st._lucia": "🇱🇨",
    "st._martin": "🇲🇫",
    "st._pierre_&_miquelon": "🇵🇲",
    "st._vincent_&_grenadines": "🇻🇨",
    "statue_of_liberty": "🗽",
    "sudan": "🇸🇩",
    "suriname": "🇸🇷",
    "svalbard_&_jan_mayen": "🇸🇯",
    "swaziland": "🇸🇿",
    "sweden": "🇸🇪",
    "switzerland": "🇨🇭",
    "syria": "🇸🇾",
    "são_tomé_&_príncipe": "🇸🇹",
    "t-rex": "🦖",
    "top_arrow": "🔝",
    "taiwan": "🇹🇼",
    "tajikistan": "🇹🇯",
    "tanzania": "🇹🇿",
    "taurus": "♉",
    "thailand": "🇹🇭",
    "timor-leste": "🇹🇱",
    "togo": "🇹🇬",
    "tokelau": "🇹🇰",
    "tokyo_tower": "🗼",
    "tonga": "🇹🇴",
    "trinidad_&_tobago": "🇹🇹",
    "tristan_da_cunha": "🇹🇦",
    "tunisia": "🇹🇳",
    "turkey": "🦃",
    "turkmenistan": "🇹🇲",
    "turks_&_caicos_islands": "🇹🇨",
    "tuvalu": "🇹🇻",
    "u.s._outlying_islands": "🇺🇲",
    "u.s._virgin_islands": "🇻🇮",
    "up!_button": "🆙",
    "uganda": "🇺🇬",
    "ukraine": "🇺🇦",
    "united_arab_emirates": "🇦🇪",
    "united_kingdom": "🇬🇧",
    "united_nations": "🇺🇳",
    "united_states": "🇺🇸",
    "uruguay": "🇺🇾",
    "uzbekistan": "🇺🇿",
    "vs_button": "🆚",
    "vanuatu": "🇻🇺",
    "vatican_city": "🇻🇦",
    "venezuela": "🇻🇪",
    "vietnam": "🇻🇳",
    "virgo": "♍",
    "wales": "🏴\U000e0067\U000e0062\U000e0077\U000e006c\U000e0073\U000e007f",
    "wallis_&_futuna": "🇼🇫",
    "western_sahara": "🇪🇭",
    "yemen": "🇾🇪",
    "zambia": "🇿🇲",
    "zimbabwe": "🇿🇼",
    "abacus": "🧮",
    "adhesive_bandage": "🩹",
    "admission_tickets": "🎟",
    "adult": "🧑",
    "adult_dark_skin_tone": "🧑🏿",
    "adult_light_skin_tone": "🧑🏻",
    "adult_medium-dark_skin_tone": "🧑🏾",
    "adult_medium-light_skin_tone": "🧑🏼",
    "adult_medium_skin_tone": "🧑🏽",
    "aerial_tramway": "🚡",
    "airplane": "✈",
    "airplane_arrival": "🛬",
    "airplane_departure": "🛫",
    "alarm_clock": "⏰",
    "alembic": "⚗",
    "alien": "👽",
    "alien_monster": "👾",
    "ambulance": "🚑",
    "american_football": "🏈",
    "amphora": "🏺",
    "anchor": "⚓",
    "anger_symbol": "💢",
    "angry_face": "😠",
    "angry_face_with_horns": "👿",
    "anguished_face": "😧",
    "ant": "🐜",
    "antenna_bars": "📶",
    "anxious_face_with_sweat": "😰",
    "articulated_lorry": "🚛",
    "artist_palette": "🎨",
    "astonished_face": "😲",
    "atom_symbol": "⚛",
    "auto_rickshaw": "🛺",
    "automobile": "🚗",
    "avocado": "🥑",
    "axe": "🪓",
    "baby": "👶",
    "baby_angel": "👼",
    "baby_angel_dark_skin_tone": "👼🏿",
    "baby_angel_light_skin_tone": "👼🏻",
    "baby_angel_medium-dark_skin_tone": "👼🏾",
    "baby_angel_medium-light_skin_tone": "👼🏼",
    "baby_angel_medium_skin_tone": "👼🏽",
    "baby_bottle": "🍼",
    "baby_chick": "🐤",
    "baby_dark_skin_tone": "👶🏿",
    "baby_light_skin_tone": "👶🏻",
    "baby_medium-dark_skin_tone": "👶🏾",
    "baby_medium-light_skin_tone": "👶🏼",
    "baby_medium_skin_tone": "👶🏽",
    "baby_symbol": "🚼",
    "backhand_index_pointing_down": "👇",
    "backhand_index_pointing_down_dark_skin_tone": "👇🏿",
    "backhand_index_pointing_down_light_skin_tone": "👇🏻",
    "backhand_index_pointing_down_medium-dark_skin_tone": "👇🏾",
    "backhand_index_pointing_down_medium-light_skin_tone": "👇🏼",
    "backhand_index_pointing_down_medium_skin_tone": "👇🏽",
    "backhand_index_pointing_left": "👈",
    "backhand_index_pointing_left_dark_skin_tone": "👈🏿",
    "backhand_index_pointing_left_light_skin_tone": "👈🏻",
    "backhand_index_pointing_left_medium-dark_skin_tone": "👈🏾",
    "backhand_index_pointing_left_medium-light_skin_tone": "👈🏼",
    "backhand_index_pointing_left_medium_skin_tone": "👈🏽",
    "backhand_index_pointing_right": "👉",
    "backhand_index_pointing_right_dark_skin_tone": "👉🏿",
    "backhand_index_pointing_right_light_skin_tone": "👉🏻",
    "backhand_index_pointing_right_medium-dark_skin_tone": "👉🏾",
    "backhand_index_pointing_right_medium-light_skin_tone": "👉🏼",
    "backhand_index_pointing_right_medium_skin_tone": "👉🏽",
    "backhand_index_pointing_up": "👆",
    "backhand_index_pointing_up_dark_skin_tone": "👆🏿",
    "backhand_index_pointing_up_light_skin_tone": "👆🏻",
    "backhand_index_pointing_up_medium-dark_skin_tone": "👆🏾",
    "backhand_index_pointing_up_medium-light_skin_tone": "👆🏼",
    "backhand_index_pointing_up_medium_skin_tone": "👆🏽",
    "bacon": "🥓",
    "badger": "🦡",
    "badminton": "🏸",
    "bagel": "🥯",
    "baggage_claim": "🛄",
    "baguette_bread": "🥖",
    "balance_scale": "⚖",
    "bald": "🦲",
    "bald_man": "👨\u200d🦲",
    "bald_woman": "👩\u200d🦲",
    "ballet_shoes": "🩰",
    "balloon": "🎈",
    "ballot_box_with_ballot": "🗳",
    "ballot_box_with_check": "☑",
    "banana": "🍌",
    "banjo": "🪕",
    "bank": "🏦",
    "bar_chart": "📊",
    "barber_pole": "💈",
    "baseball": "⚾",
    "basket": "🧺",
    "basketball": "🏀",
    "bat": "🦇",
    "bathtub": "🛁",
    "battery": "🔋",
    "beach_with_umbrella": "🏖",
    "beaming_face_with_smiling_eyes": "😁",
    "bear_face": "🐻",
    "bearded_person": "🧔",
    "bearded_person_dark_skin_tone": "🧔🏿",
    "bearded_person_light_skin_tone": "🧔🏻",
    "bearded_person_medium-dark_skin_tone": "🧔🏾",
    "bearded_person_medium-light_skin_tone": "🧔🏼",
    "bearded_person_medium_skin_tone": "🧔🏽",
    "beating_heart": "💓",
    "bed": "🛏",
    "beer_mug": "🍺",
    "bell": "🔔",
    "bell_with_slash": "🔕",
    "bellhop_bell": "🛎",
    "bento_box": "🍱",
    "beverage_box": "🧃",
    "bicycle": "🚲",
    "bikini": "👙",
    "billed_cap": "🧢",
    "biohazard": "☣",
    "bird": "🐦",
    "birthday_cake": "🎂",
    "black_circle": "⚫",
    "black_flag": "🏴",
    "black_heart": "🖤",
    "black_large_square": "⬛",
    "black_medium-small_square": "◾",
    "black_medium_square": "◼",
    "black_nib": "✒",
    "black_small_square": "▪",
    "black_square_button": "🔲",
    "blond-haired_man": "👱\u200d♂️",
    "blond-haired_man_dark_skin_tone": "👱🏿\u200d♂️",
    "blond-haired_man_light_skin_tone": "👱🏻\u200d♂️",
    "blond-haired_man_medium-dark_skin_tone": "👱🏾\u200d♂️",
    "blond-haired_man_medium-light_skin_tone": "👱🏼\u200d♂️",
    "blond-haired_man_medium_skin_tone": "👱🏽\u200d♂️",
    "blond-haired_person": "👱",
    "blond-haired_person_dark_skin_tone": "👱🏿",
    "blond-haired_person_light_skin_tone": "👱🏻",
    "blond-haired_person_medium-dark_skin_tone": "👱🏾",
    "blond-haired_person_medium-light_skin_tone": "👱🏼",
    "blond-haired_person_medium_skin_tone": "👱🏽",
    "blond-haired_woman": "👱\u200d♀️",
    "blond-haired_woman_dark_skin_tone": "👱🏿\u200d♀️",
    "blond-haired_woman_light_skin_tone": "👱🏻\u200d♀️",
    "blond-haired_woman_medium-dark_skin_tone": "👱🏾\u200d♀️",
    "blond-haired_woman_medium-light_skin_tone": "👱🏼\u200d♀️",
    "blond-haired_woman_medium_skin_tone": "👱🏽\u200d♀️",
    "blossom": "🌼",
    "blowfish": "🐡",
    "blue_book": "📘",
    "blue_circle": "🔵",
    "blue_heart": "💙",
    "blue_square": "🟦",
    "boar": "🐗",
    "bomb": "💣",
    "bone": "🦴",
    "bookmark": "🔖",
    "bookmark_tabs": "📑",
    "books": "📚",
    "bottle_with_popping_cork": "🍾",
    "bouquet": "💐",
    "bow_and_arrow": "🏹",
    "bowl_with_spoon": "🥣",
    "bowling": "🎳",
    "boxing_glove": "🥊",
    "boy": "👦",
    "boy_dark_skin_tone": "👦🏿",
    "boy_light_skin_tone": "👦🏻",
    "boy_medium-dark_skin_tone": "👦🏾",
    "boy_medium-light_skin_tone": "👦🏼",
    "boy_medium_skin_tone": "👦🏽",
    "brain": "🧠",
    "bread": "🍞",
    "breast-feeding": "🤱",
    "breast-feeding_dark_skin_tone": "🤱🏿",
    "breast-feeding_light_skin_tone": "🤱🏻",
    "breast-feeding_medium-dark_skin_tone": "🤱🏾",
    "breast-feeding_medium-light_skin_tone": "🤱🏼",
    "breast-feeding_medium_skin_tone": "🤱🏽",
    "brick": "🧱",
    "bride_with_veil": "👰",
    "bride_with_veil_dark_skin_tone": "👰🏿",
    "bride_with_veil_light_skin_tone": "👰🏻",
    "bride_with_veil_medium-dark_skin_tone": "👰🏾",
    "bride_with_veil_medium-light_skin_tone": "👰🏼",
    "bride_with_veil_medium_skin_tone": "👰🏽",
    "bridge_at_night": "🌉",
    "briefcase": "💼",
    "briefs": "🩲",
    "bright_button": "🔆",
    "broccoli": "🥦",
    "broken_heart": "💔",
    "broom": "🧹",
    "brown_circle": "🟤",
    "brown_heart": "🤎",
    "brown_square": "🟫",
    "bug": "🐛",
    "building_construction": "🏗",
    "bullet_train": "🚅",
    "burrito": "🌯",
    "bus": "🚌",
    "bus_stop": "🚏",
    "bust_in_silhouette": "👤",
    "busts_in_silhouette": "👥",
    "butter": "🧈",
    "butterfly": "🦋",
    "cactus": "🌵",
    "calendar": "📆",
    "call_me_hand": "🤙",
    "call_me_hand_dark_skin_tone": "🤙🏿",
    "call_me_hand_light_skin_tone": "🤙🏻",
    "call_me_hand_medium-dark_skin_tone": "🤙🏾",
    "call_me_hand_medium-light_skin_tone": "🤙🏼",
    "call_me_hand_medium_skin_tone": "🤙🏽",
    "camel": "🐫",
    "camera": "📷",
    "camera_with_flash": "📸",
    "camping": "🏕",
    "candle": "🕯",
    "candy": "🍬",
    "canned_food": "🥫",
    "canoe": "🛶",
    "card_file_box": "🗃",
    "card_index": "📇",
    "card_index_dividers": "🗂",
    "carousel_horse": "🎠",
    "carp_streamer": "🎏",
    "carrot": "🥕",
    "castle": "🏰",
    "cat": "🐱",
    "cat_face": "🐱",
    "cat_face_with_tears_of_joy": "😹",
    "cat_face_with_wry_smile": "😼",
    "chains": "⛓",
    "chair": "🪑",
    "chart_decreasing": "📉",
    "chart_increasing": "📈",
    "chart_increasing_with_yen": "💹",
    "cheese_wedge": "🧀",
    "chequered_flag": "🏁",
    "cherries": "🍒",
    "cherry_blossom": "🌸",
    "chess_pawn": "♟",
    "chestnut": "🌰",
    "chicken": "🐔",
    "child": "🧒",
    "child_dark_skin_tone": "🧒🏿",
    "child_light_skin_tone": "🧒🏻",
    "child_medium-dark_skin_tone": "🧒🏾",
    "child_medium-light_skin_tone": "🧒🏼",
    "child_medium_skin_tone": "🧒🏽",
    "children_crossing": "🚸",
    "chipmunk": "🐿",
    "chocolate_bar": "🍫",
    "chopsticks": "🥢",
    "church": "⛪",
    "cigarette": "🚬",
    "cinema": "🎦",
    "circled_m": "Ⓜ",
    "circus_tent": "🎪",
    "cityscape": "🏙",
    "cityscape_at_dusk": "🌆",
    "clamp": "🗜",
    "clapper_board": "🎬",
    "clapping_hands": "👏",
    "clapping_hands_dark_skin_tone": "👏🏿",
    "clapping_hands_light_skin_tone": "👏🏻",
    "clapping_hands_medium-dark_skin_tone": "👏🏾",
    "clapping_hands_medium-light_skin_tone": "👏🏼",
    "clapping_hands_medium_skin_tone": "👏🏽",
    "classical_building": "🏛",
    "clinking_beer_mugs": "🍻",
    "clinking_glasses": "🥂",
    "clipboard": "📋",
    "clockwise_vertical_arrows": "🔃",
    "closed_book": "📕",
    "closed_mailbox_with_lowered_flag": "📪",
    "closed_mailbox_with_raised_flag": "📫",
    "closed_umbrella": "🌂",
    "cloud": "☁",
    "cloud_with_lightning": "🌩",
    "cloud_with_lightning_and_rain": "⛈",
    "cloud_with_rain": "🌧",
    "cloud_with_snow": "🌨",
    "clown_face": "🤡",
    "club_suit": "♣",
    "clutch_bag": "👝",
    "coat": "🧥",
    "cocktail_glass": "🍸",
    "coconut": "🥥",
    "coffin": "⚰",
    "cold_face": "🥶",
    "collision": "💥",
    "comet": "☄",
    "compass": "🧭",
    "computer_disk": "💽",
    "computer_mouse": "🖱",
    "confetti_ball": "🎊",
    "confounded_face": "😖",
    "confused_face": "😕",
    "construction": "🚧",
    "construction_worker": "👷",
    "construction_worker_dark_skin_tone": "👷🏿",
    "construction_worker_light_skin_tone": "👷🏻",
    "construction_worker_medium-dark_skin_tone": "👷🏾",
    "construction_worker_medium-light_skin_tone": "👷🏼",
    "construction_worker_medium_skin_tone": "👷🏽",
    "control_knobs": "🎛",
    "convenience_store": "🏪",
    "cooked_rice": "🍚",
    "cookie": "🍪",
    "cooking": "🍳",
    "copyright": "©",
    "couch_and_lamp": "🛋",
    "counterclockwise_arrows_button": "🔄",
    "couple_with_heart": "💑",
    "couple_with_heart_man_man": "👨\u200d❤️\u200d👨",
    "couple_with_heart_woman_man": "👩\u200d❤️\u200d👨",
    "couple_with_heart_woman_woman": "👩\u200d❤️\u200d👩",
    "cow": "🐮",
    "cow_face": "🐮",
    "cowboy_hat_face": "🤠",
    "crab": "🦀",
    "crayon": "🖍",
    "credit_card": "💳",
    "crescent_moon": "🌙",
    "cricket": "🦗",
    "cricket_game": "🏏",
    "crocodile": "🐊",
    "croissant": "🥐",
    "cross_mark": "❌",
    "cross_mark_button": "❎",
    "crossed_fingers": "🤞",
    "crossed_fingers_dark_skin_tone": "🤞🏿",
    "crossed_fingers_light_skin_tone": "🤞🏻",
    "crossed_fingers_medium-dark_skin_tone": "🤞🏾",
    "crossed_fingers_medium-light_skin_tone": "🤞🏼",
    "crossed_fingers_medium_skin_tone": "🤞🏽",
    "crossed_flags": "🎌",
    "crossed_swords": "⚔",
    "crown": "👑",
    "crying_cat_face": "😿",
    "crying_face": "😢",
    "crystal_ball": "🔮",
    "cucumber": "🥒",
    "cupcake": "🧁",
    "cup_with_straw": "🥤",
    "curling_stone": "🥌",
    "curly_hair": "🦱",
    "curly-haired_man": "👨\u200d🦱",
    "curly-haired_woman": "👩\u200d🦱",
    "curly_loop": "➰",
    "currency_exchange": "💱",
    "curry_rice": "🍛",
    "custard": "🍮",
    "customs": "🛃",
    "cut_of_meat": "🥩",
    "cyclone": "🌀",
    "dagger": "🗡",
    "dango": "🍡",
    "dashing_away": "💨",
    "deaf_person": "🧏",
    "deciduous_tree": "🌳",
    "deer": "🦌",
    "delivery_truck": "🚚",
    "department_store": "🏬",
    "derelict_house": "🏚",
    "desert": "🏜",
    "desert_island": "🏝",
    "desktop_computer": "🖥",
    "detective": "🕵",
    "detective_dark_skin_tone": "🕵🏿",
    "detective_light_skin_tone": "🕵🏻",
    "detective_medium-dark_skin_tone": "🕵🏾",
    "detective_medium-light_skin_tone": "🕵🏼",
    "detective_medium_skin_tone": "🕵🏽",
    "diamond_suit": "♦",
    "diamond_with_a_dot": "💠",
    "dim_button": "🔅",
    "direct_hit": "🎯",
    "disappointed_face": "😞",
    "diving_mask": "🤿",
    "diya_lamp": "🪔",
    "dizzy": "💫",
    "dizzy_face": "😵",
    "dna": "🧬",
    "dog": "🐶",
    "dog_face": "🐶",
    "dollar_banknote": "💵",
    "dolphin": "🐬",
    "door": "🚪",
    "dotted_six-pointed_star": "🔯",
    "double_curly_loop": "➿",
    "double_exclamation_mark": "‼",
    "doughnut": "🍩",
    "dove": "🕊",
    "down-left_arrow": "↙",
    "down-right_arrow": "↘",
    "down_arrow": "⬇",
    "downcast_face_with_sweat": "😓",
    "downwards_button": "🔽",
    "dragon": "🐉",
    "dragon_face": "🐲",
    "dress": "👗",
    "drooling_face": "🤤",
    "drop_of_blood": "🩸",
    "droplet": "💧",
    "drum": "🥁",
    "duck": "🦆",
    "dumpling": "🥟",
    "dvd": "📀",
    "e-mail": "📧",
    "eagle": "🦅",
    "ear": "👂",
    "ear_dark_skin_tone": "👂🏿",
    "ear_light_skin_tone": "👂🏻",
    "ear_medium-dark_skin_tone": "👂🏾",
    "ear_medium-light_skin_tone": "👂🏼",
    "ear_medium_skin_tone": "👂🏽",
    "ear_of_corn": "🌽",
    "ear_with_hearing_aid": "🦻",
    "egg": "🍳",
    "eggplant": "🍆",
    "eight-pointed_star": "✴",
    "eight-spoked_asterisk": "✳",
    "eight-thirty": "🕣",
    "eight_o’clock": "🕗",
    "eject_button": "⏏",
    "electric_plug": "🔌",
    "elephant": "🐘",
    "eleven-thirty": "🕦",
    "eleven_o’clock": "🕚",
    "elf": "🧝",
    "elf_dark_skin_tone": "🧝🏿",
    "elf_light_skin_tone": "🧝🏻",
    "elf_medium-dark_skin_tone": "🧝🏾",
    "elf_medium-light_skin_tone": "🧝🏼",
    "elf_medium_skin_tone": "🧝🏽",
    "envelope": "✉",
    "envelope_with_arrow": "📩",
    "euro_banknote": "💶",
    "evergreen_tree": "🌲",
    "ewe": "🐑",
    "exclamation_mark": "❗",
    "exclamation_question_mark": "⁉",
    "exploding_head": "🤯",
    "expressionless_face": "😑",
    "eye": "👁",
    "eye_in_speech_bubble": "👁️\u200d🗨️",
    "eyes": "👀",
    "face_blowing_a_kiss": "😘",
    "face_savoring_food": "😋",
    "face_screaming_in_fear": "😱",
    "face_vomiting": "🤮",
    "face_with_hand_over_mouth": "🤭",
    "face_with_head-bandage": "🤕",
    "face_with_medical_mask": "😷",
    "face_with_monocle": "🧐",
    "face_with_open_mouth": "😮",
    "face_with_raised_eyebrow": "🤨",
    "face_with_rolling_eyes": "🙄",
    "face_with_steam_from_nose": "😤",
    "face_with_symbols_on_mouth": "🤬",
    "face_with_tears_of_joy": "😂",
    "face_with_thermometer": "🤒",
    "face_with_tongue": "😛",
    "face_without_mouth": "😶",
    "factory": "🏭",
    "fairy": "🧚",
    "fairy_dark_skin_tone": "🧚🏿",
    "fairy_light_skin_tone": "🧚🏻",
    "fairy_medium-dark_skin_tone": "🧚🏾",
    "fairy_medium-light_skin_tone": "🧚🏼",
    "fairy_medium_skin_tone": "🧚🏽",
    "falafel": "🧆",
    "fallen_leaf": "🍂",
    "family": "👪",
    "family_man_boy": "👨\u200d👦",
    "family_man_boy_boy": "👨\u200d👦\u200d👦",
    "family_man_girl": "👨\u200d👧",
    "family_man_girl_boy": "👨\u200d👧\u200d👦",
    "family_man_girl_girl": "👨\u200d👧\u200d👧",
    "family_man_man_boy": "👨\u200d👨\u200d👦",
    "family_man_man_boy_boy": "👨\u200d👨\u200d👦\u200d👦",
    "family_man_man_girl": "👨\u200d👨\u200d👧",
    "family_man_man_girl_boy": "👨\u200d👨\u200d👧\u200d👦",
    "family_man_man_girl_girl": "👨\u200d👨\u200d👧\u200d👧",
    "family_man_woman_boy": "👨\u200d👩\u200d👦",
    "family_man_woman_boy_boy": "👨\u200d👩\u200d👦\u200d👦",
    "family_man_woman_girl": "👨\u200d👩\u200d👧",
    "family_man_woman_girl_boy": "👨\u200d👩\u200d👧\u200d👦",
    "family_man_woman_girl_girl": "👨\u200d👩\u200d👧\u200d👧",
    "family_woman_boy": "👩\u200d👦",
    "family_woman_boy_boy": "👩\u200d👦\u200d👦",
    "family_woman_girl": "👩\u200d👧",
    "family_woman_girl_boy": "👩\u200d👧\u200d👦",
    "family_woman_girl_girl": "👩\u200d👧\u200d👧",
    "family_woman_woman_boy": "👩\u200d👩\u200d👦",
    "family_woman_woman_boy_boy": "👩\u200d👩\u200d👦\u200d👦",
    "family_woman_woman_girl": "👩\u200d👩\u200d👧",
    "family_woman_woman_girl_boy": "👩\u200d👩\u200d👧\u200d👦",
    "family_woman_woman_girl_girl": "👩\u200d👩\u200d👧\u200d👧",
    "fast-forward_button": "⏩",
    "fast_down_button": "⏬",
    "fast_reverse_button": "⏪",
    "fast_up_button": "⏫",
    "fax_machine": "📠",
    "fearful_face": "😨",
    "female_sign": "♀",
    "ferris_wheel": "🎡",
    "ferry": "⛴",
    "field_hockey": "🏑",
    "file_cabinet": "🗄",
    "file_folder": "📁",
    "film_frames": "🎞",
    "film_projector": "📽",
    "fire": "🔥",
    "fire_extinguisher": "🧯",
    "firecracker": "🧨",
    "fire_engine": "🚒",
    "fireworks": "🎆",
    "first_quarter_moon": "🌓",
    "first_quarter_moon_face": "🌛",
    "fish": "🐟",
    "fish_cake_with_swirl": "🍥",
    "fishing_pole": "🎣",
    "five-thirty": "🕠",
    "five_o’clock": "🕔",
    "flag_in_hole": "⛳",
    "flamingo": "🦩",
    "flashlight": "🔦",
    "flat_shoe": "🥿",
    "fleur-de-lis": "⚜",
    "flexed_biceps": "💪",
    "flexed_biceps_dark_skin_tone": "💪🏿",
    "flexed_biceps_light_skin_tone": "💪🏻",
    "flexed_biceps_medium-dark_skin_tone": "💪🏾",
    "flexed_biceps_medium-light_skin_tone": "💪🏼",
    "flexed_biceps_medium_skin_tone": "💪🏽",
    "floppy_disk": "💾",
    "flower_playing_cards": "🎴",
    "flushed_face": "😳",
    "flying_disc": "🥏",
    "flying_saucer": "🛸",
    "fog": "🌫",
    "foggy": "🌁",
    "folded_hands": "🙏",
    "folded_hands_dark_skin_tone": "🙏🏿",
    "folded_hands_light_skin_tone": "🙏🏻",
    "folded_hands_medium-dark_skin_tone": "🙏🏾",
    "folded_hands_medium-light_skin_tone": "🙏🏼",
    "folded_hands_medium_skin_tone": "🙏🏽",
    "foot": "🦶",
    "footprints": "👣",
    "fork_and_knife": "🍴",
    "fork_and_knife_with_plate": "🍽",
    "fortune_cookie": "🥠",
    "fountain": "⛲",
    "fountain_pen": "🖋",
    "four-thirty": "🕟",
    "four_leaf_clover": "🍀",
    "four_o’clock": "🕓",
    "fox_face": "🦊",
    "framed_picture": "🖼",
    "french_fries": "🍟",
    "fried_shrimp": "🍤",
    "frog_face": "🐸",
    "front-facing_baby_chick": "🐥",
    "frowning_face": "☹",
    "frowning_face_with_open_mouth": "😦",
    "fuel_pump": "⛽",
    "full_moon": "🌕",
    "full_moon_face": "🌝",
    "funeral_urn": "⚱",
    "game_die": "🎲",
    "garlic": "🧄",
    "gear": "⚙",
    "gem_stone": "💎",
    "genie": "🧞",
    "ghost": "👻",
    "giraffe": "🦒",
    "girl": "👧",
    "girl_dark_skin_tone": "👧🏿",
    "girl_light_skin_tone": "👧🏻",
    "girl_medium-dark_skin_tone": "👧🏾",
    "girl_medium-light_skin_tone": "👧🏼",
    "girl_medium_skin_tone": "👧🏽",
    "glass_of_milk": "🥛",
    "glasses": "👓",
    "globe_showing_americas": "🌎",
    "globe_showing_asia-australia": "🌏",
    "globe_showing_europe-africa": "🌍",
    "globe_with_meridians": "🌐",
    "gloves": "🧤",
    "glowing_star": "🌟",
    "goal_net": "🥅",
    "goat": "🐐",
    "goblin": "👺",
    "goggles": "🥽",
    "gorilla": "🦍",
    "graduation_cap": "🎓",
    "grapes": "🍇",
    "green_apple": "🍏",
    "green_book": "📗",
    "green_circle": "🟢",
    "green_heart": "💚",
    "green_salad": "🥗",
    "green_square": "🟩",
    "grimacing_face": "😬",
    "grinning_cat_face": "😺",
    "grinning_cat_face_with_smiling_eyes": "😸",
    "grinning_face": "😀",
    "grinning_face_with_big_eyes": "😃",
    "grinning_face_with_smiling_eyes": "😄",
    "grinning_face_with_sweat": "😅",
    "grinning_squinting_face": "😆",
    "growing_heart": "💗",
    "guard": "💂",
    "guard_dark_skin_tone": "💂🏿",
    "guard_light_skin_tone": "💂🏻",
    "guard_medium-dark_skin_tone": "💂🏾",
    "guard_medium-light_skin_tone": "💂🏼",
    "guard_medium_skin_tone": "💂🏽",
    "guide_dog": "🦮",
    "guitar": "🎸",
    "hamburger": "🍔",
    "hammer": "🔨",
    "hammer_and_pick": "⚒",
    "hammer_and_wrench": "🛠",
    "hamster_face": "🐹",
    "hand_with_fingers_splayed": "🖐",
    "hand_with_fingers_splayed_dark_skin_tone": "🖐🏿",
    "hand_with_fingers_splayed_light_skin_tone": "🖐🏻",
    "hand_with_fingers_splayed_medium-dark_skin_tone": "🖐🏾",
    "hand_with_fingers_splayed_medium-light_skin_tone": "🖐🏼",
    "hand_with_fingers_splayed_medium_skin_tone": "🖐🏽",
    "handbag": "👜",
    "handshake": "🤝",
    "hatching_chick": "🐣",
    "headphone": "🎧",
    "hear-no-evil_monkey": "🙉",
    "heart_decoration": "💟",
    "heart_suit": "♥",
    "heart_with_arrow": "💘",
    "heart_with_ribbon": "💝",
    "heavy_check_mark": "✔",
    "heavy_division_sign": "➗",
    "heavy_dollar_sign": "💲",
    "heavy_heart_exclamation": "❣",
    "heavy_large_circle": "⭕",
    "heavy_minus_sign": "➖",
    "heavy_multiplication_x": "✖",
    "heavy_plus_sign": "➕",
    "hedgehog": "🦔",
    "helicopter": "🚁",
    "herb": "🌿",
    "hibiscus": "🌺",
    "high-heeled_shoe": "👠",
    "high-speed_train": "🚄",
    "high_voltage": "⚡",
    "hiking_boot": "🥾",
    "hindu_temple": "🛕",
    "hippopotamus": "🦛",
    "hole": "🕳",
    "honey_pot": "🍯",
    "honeybee": "🐝",
    "horizontal_traffic_light": "🚥",
    "horse": "🐴",
    "horse_face": "🐴",
    "horse_racing": "🏇",
    "horse_racing_dark_skin_tone": "🏇🏿",
    "horse_racing_light_skin_tone": "🏇🏻",
    "horse_racing_medium-dark_skin_tone": "🏇🏾",
    "horse_racing_medium-light_skin_tone": "🏇🏼",
    "horse_racing_medium_skin_tone": "🏇🏽",
    "hospital": "🏥",
    "hot_beverage": "☕",
    "hot_dog": "🌭",
    "hot_face": "🥵",
    "hot_pepper": "🌶",
    "hot_springs": "♨",
    "hotel": "🏨",
    "hourglass_done": "⌛",
    "hourglass_not_done": "⏳",
    "house": "🏠",
    "house_with_garden": "🏡",
    "houses": "🏘",
    "hugging_face": "🤗",
    "hundred_points": "💯",
    "hushed_face": "😯",
    "ice": "🧊",
    "ice_cream": "🍨",
    "ice_hockey": "🏒",
    "ice_skate": "⛸",
    "inbox_tray": "📥",
    "incoming_envelope": "📨",
    "index_pointing_up": "☝",
    "index_pointing_up_dark_skin_tone": "☝🏿",
    "index_pointing_up_light_skin_tone": "☝🏻",
    "index_pointing_up_medium-dark_skin_tone": "☝🏾",
    "index_pointing_up_medium-light_skin_tone": "☝🏼",
    "index_pointing_up_medium_skin_tone": "☝🏽",
    "infinity": "♾",
    "information": "ℹ",
    "input_latin_letters": "🔤",
    "input_latin_lowercase": "🔡",
    "input_latin_uppercase": "🔠",
    "input_numbers": "🔢",
    "input_symbols": "🔣",
    "jack-o-lantern": "🎃",
    "jeans": "👖",
    "jigsaw": "🧩",
    "joker": "🃏",
    "joystick": "🕹",
    "kaaba": "🕋",
    "kangaroo": "🦘",
    "key": "🔑",
    "keyboard": "⌨",
    "keycap_#": "#️⃣",
    "keycap_*": "*️⃣",
    "keycap_0": "0️⃣",
    "keycap_1": "1️⃣",
    "keycap_10": "🔟",
    "keycap_2": "2️⃣",
    "keycap_3": "3️⃣",
    "keycap_4": "4️⃣",
    "keycap_5": "5️⃣",
    "keycap_6": "6️⃣",
    "keycap_7": "7️⃣",
    "keycap_8": "8️⃣",
    "keycap_9": "9️⃣",
    "kick_scooter": "🛴",
    "kimono": "👘",
    "kiss": "💋",
    "kiss_man_man": "👨\u200d❤️\u200d💋\u200d👨",
    "kiss_mark": "💋",
    "kiss_woman_man": "👩\u200d❤️\u200d💋\u200d👨",
    "kiss_woman_woman": "👩\u200d❤️\u200d💋\u200d👩",
    "kissing_cat_face": "😽",
    "kissing_face": "😗",
    "kissing_face_with_closed_eyes": "😚",
    "kissing_face_with_smiling_eyes": "😙",
    "kitchen_knife": "🔪",
    "kite": "🪁",
    "kiwi_fruit": "🥝",
    "koala": "🐨",
    "lab_coat": "🥼",
    "label": "🏷",
    "lacrosse": "🥍",
    "lady_beetle": "🐞",
    "laptop_computer": "💻",
    "large_blue_diamond": "🔷",
    "large_orange_diamond": "🔶",
    "last_quarter_moon": "🌗",
    "last_quarter_moon_face": "🌜",
    "last_track_button": "⏮",
    "latin_cross": "✝",
    "leaf_fluttering_in_wind": "🍃",
    "leafy_green": "🥬",
    "ledger": "📒",
    "left-facing_fist": "🤛",
    "left-facing_fist_dark_skin_tone": "🤛🏿",
    "left-facing_fist_light_skin_tone": "🤛🏻",
    "left-facing_fist_medium-dark_skin_tone": "🤛🏾",
    "left-facing_fist_medium-light_skin_tone": "🤛🏼",
    "left-facing_fist_medium_skin_tone": "🤛🏽",
    "left-right_arrow": "↔",
    "left_arrow": "⬅",
    "left_arrow_curving_right": "↪",
    "left_luggage": "🛅",
    "left_speech_bubble": "🗨",
    "leg": "🦵",
    "lemon": "🍋",
    "leopard": "🐆",
    "level_slider": "🎚",
    "light_bulb": "💡",
    "light_rail": "🚈",
    "link": "🔗",
    "linked_paperclips": "🖇",
    "lion_face": "🦁",
    "lipstick": "💄",
    "litter_in_bin_sign": "🚮",
    "lizard": "🦎",
    "llama": "🦙",
    "lobster": "🦞",
    "locked": "🔒",
    "locked_with_key": "🔐",
    "locked_with_pen": "🔏",
    "locomotive": "🚂",
    "lollipop": "🍭",
    "lotion_bottle": "🧴",
    "loudly_crying_face": "😭",
    "loudspeaker": "📢",
    "love-you_gesture": "🤟",
    "love-you_gesture_dark_skin_tone": "🤟🏿",
    "love-you_gesture_light_skin_tone": "🤟🏻",
    "love-you_gesture_medium-dark_skin_tone": "🤟🏾",
    "love-you_gesture_medium-light_skin_tone": "🤟🏼",
    "love-you_gesture_medium_skin_tone": "🤟🏽",
    "love_hotel": "🏩",
    "love_letter": "💌",
    "luggage": "🧳",
    "lying_face": "🤥",
    "mage": "🧙",
    "mage_dark_skin_tone": "🧙🏿",
    "mage_light_skin_tone": "🧙🏻",
    "mage_medium-dark_skin_tone": "🧙🏾",
    "mage_medium-light_skin_tone": "🧙🏼",
    "mage_medium_skin_tone": "🧙🏽",
    "magnet": "🧲",
    "magnifying_glass_tilted_left": "🔍",
    "magnifying_glass_tilted_right": "🔎",
    "mahjong_red_dragon": "🀄",
    "male_sign": "♂",
    "man": "👨",
    "man_and_woman_holding_hands": "👫",
    "man_artist": "👨\u200d🎨",
    "man_artist_dark_skin_tone": "👨🏿\u200d🎨",
    "man_artist_light_skin_tone": "👨🏻\u200d🎨",
    "man_artist_medium-dark_skin_tone": "👨🏾\u200d🎨",
    "man_artist_medium-light_skin_tone": "👨🏼\u200d🎨",
    "man_artist_medium_skin_tone": "👨🏽\u200d🎨",
    "man_astronaut": "👨\u200d🚀",
    "man_astronaut_dark_skin_tone": "👨🏿\u200d🚀",
    "man_astronaut_light_skin_tone": "👨🏻\u200d🚀",
    "man_astronaut_medium-dark_skin_tone": "👨🏾\u200d🚀",
    "man_astronaut_medium-light_skin_tone": "👨🏼\u200d🚀",
    "man_astronaut_medium_skin_tone": "👨🏽\u200d🚀",
    "man_biking": "🚴\u200d♂️",
    "man_biking_dark_skin_tone": "🚴🏿\u200d♂️",
    "man_biking_light_skin_tone": "🚴🏻\u200d♂️",
    "man_biking_medium-dark_skin_tone": "🚴🏾\u200d♂️",
    "man_biking_medium-light_skin_tone": "🚴🏼\u200d♂️",
    "man_biking_medium_skin_tone": "🚴🏽\u200d♂️",
    "man_bouncing_ball": "⛹️\u200d♂️",
    "man_bouncing_ball_dark_skin_tone": "⛹🏿\u200d♂️",
    "man_bouncing_ball_light_skin_tone": "⛹🏻\u200d♂️",
    "man_bouncing_ball_medium-dark_skin_tone": "⛹🏾\u200d♂️",
    "man_bouncing_ball_medium-light_skin_tone": "⛹🏼\u200d♂️",
    "man_bouncing_ball_medium_skin_tone": "⛹🏽\u200d♂️",
    "man_bowing": "🙇\u200d♂️",
    "man_bowing_dark_skin_tone": "🙇🏿\u200d♂️",
    "man_bowing_light_skin_tone": "🙇🏻\u200d♂️",
    "man_bowing_medium-dark_skin_tone": "🙇🏾\u200d♂️",
    "man_bowing_medium-light_skin_tone": "🙇🏼\u200d♂️",
    "man_bowing_medium_skin_tone": "🙇🏽\u200d♂️",
    "man_cartwheeling": "🤸\u200d♂️",
    "man_cartwheeling_dark_skin_tone": "🤸🏿\u200d♂️",
    "man_cartwheeling_light_skin_tone": "🤸🏻\u200d♂️",
    "man_cartwheeling_medium-dark_skin_tone": "🤸🏾\u200d♂️",
    "man_cartwheeling_medium-light_skin_tone": "🤸🏼\u200d♂️",
    "man_cartwheeling_medium_skin_tone": "🤸🏽\u200d♂️",
    "man_climbing": "🧗\u200d♂️",
    "man_climbing_dark_skin_tone": "🧗🏿\u200d♂️",
    "man_climbing_light_skin_tone": "🧗🏻\u200d♂️",
    "man_climbing_medium-dark_skin_tone": "🧗🏾\u200d♂️",
    "man_climbing_medium-light_skin_tone": "🧗🏼\u200d♂️",
    "man_climbing_medium_skin_tone": "🧗🏽\u200d♂️",
    "man_construction_worker": "👷\u200d♂️",
    "man_construction_worker_dark_skin_tone": "👷🏿\u200d♂️",
    "man_construction_worker_light_skin_tone": "👷🏻\u200d♂️",
    "man_construction_worker_medium-dark_skin_tone": "👷🏾\u200d♂️",
    "man_construction_worker_medium-light_skin_tone": "👷🏼\u200d♂️",
    "man_construction_worker_medium_skin_tone": "👷🏽\u200d♂️",
    "man_cook": "👨\u200d🍳",
    "man_cook_dark_skin_tone": "👨🏿\u200d🍳",
    "man_cook_light_skin_tone": "👨🏻\u200d🍳",
    "man_cook_medium-dark_skin_tone": "👨🏾\u200d🍳",
    "man_cook_medium-light_skin_tone": "👨🏼\u200d🍳",
    "man_cook_medium_skin_tone": "👨🏽\u200d🍳",
    "man_dancing": "🕺",
    "man_dancing_dark_skin_tone": "🕺🏿",
    "man_dancing_light_skin_tone": "🕺🏻",
    "man_dancing_medium-dark_skin_tone": "🕺🏾",
    "man_dancing_medium-light_skin_tone": "🕺🏼",
    "man_dancing_medium_skin_tone": "🕺🏽",
    "man_dark_skin_tone": "👨🏿",
    "man_detective": "🕵️\u200d♂️",
    "man_detective_dark_skin_tone": "🕵🏿\u200d♂️",
    "man_detective_light_skin_tone": "🕵🏻\u200d♂️",
    "man_detective_medium-dark_skin_tone": "🕵🏾\u200d♂️",
    "man_detective_medium-light_skin_tone": "🕵🏼\u200d♂️",
    "man_detective_medium_skin_tone": "🕵🏽\u200d♂️",
    "man_elf": "🧝\u200d♂️",
    "man_elf_dark_skin_tone": "🧝🏿\u200d♂️",
    "man_elf_light_skin_tone": "🧝🏻\u200d♂️",
    "man_elf_medium-dark_skin_tone": "🧝🏾\u200d♂️",
    "man_elf_medium-light_skin_tone": "🧝🏼\u200d♂️",
    "man_elf_medium_skin_tone": "🧝🏽\u200d♂️",
    "man_facepalming": "🤦\u200d♂️",
    "man_facepalming_dark_skin_tone": "🤦🏿\u200d♂️",
    "man_facepalming_light_skin_tone": "🤦🏻\u200d♂️",
    "man_facepalming_medium-dark_skin_tone": "🤦🏾\u200d♂️",
    "man_facepalming_medium-light_skin_tone": "🤦🏼\u200d♂️",
    "man_facepalming_medium_skin_tone": "🤦🏽\u200d♂️",
    "man_factory_worker": "👨\u200d🏭",
    "man_factory_worker_dark_skin_tone": "👨🏿\u200d🏭",
    "man_factory_worker_light_skin_tone": "👨🏻\u200d🏭",
    "man_factory_worker_medium-dark_skin_tone": "👨🏾\u200d🏭",
    "man_factory_worker_medium-light_skin_tone": "👨🏼\u200d🏭",
    "man_factory_worker_medium_skin_tone": "👨🏽\u200d🏭",
    "man_fairy": "🧚\u200d♂️",
    "man_fairy_dark_skin_tone": "🧚🏿\u200d♂️",
    "man_fairy_light_skin_tone": "🧚🏻\u200d♂️",
    "man_fairy_medium-dark_skin_tone": "🧚🏾\u200d♂️",
    "man_fairy_medium-light_skin_tone": "🧚🏼\u200d♂️",
    "man_fairy_medium_skin_tone": "🧚🏽\u200d♂️",
    "man_farmer": "👨\u200d🌾",
    "man_farmer_dark_skin_tone": "👨🏿\u200d🌾",
    "man_farmer_light_skin_tone": "👨🏻\u200d🌾",
    "man_farmer_medium-dark_skin_tone": "👨🏾\u200d🌾",
    "man_farmer_medium-light_skin_tone": "👨🏼\u200d🌾",
    "man_farmer_medium_skin_tone": "👨🏽\u200d🌾",
    "man_firefighter": "👨\u200d🚒",
    "man_firefighter_dark_skin_tone": "👨🏿\u200d🚒",
    "man_firefighter_light_skin_tone": "👨🏻\u200d🚒",
    "man_firefighter_medium-dark_skin_tone": "👨🏾\u200d🚒",
    "man_firefighter_medium-light_skin_tone": "👨🏼\u200d🚒",
    "man_firefighter_medium_skin_tone": "👨🏽\u200d🚒",
    "man_frowning": "🙍\u200d♂️",
    "man_frowning_dark_skin_tone": "🙍🏿\u200d♂️",
    "man_frowning_light_skin_tone": "🙍🏻\u200d♂️",
    "man_frowning_medium-dark_skin_tone": "🙍🏾\u200d♂️",
    "man_frowning_medium-light_skin_tone": "🙍🏼\u200d♂️",
    "man_frowning_medium_skin_tone": "🙍🏽\u200d♂️",
    "man_genie": "🧞\u200d♂️",
    "man_gesturing_no": "🙅\u200d♂️",
    "man_gesturing_no_dark_skin_tone": "🙅🏿\u200d♂️",
    "man_gesturing_no_light_skin_tone": "🙅🏻\u200d♂️",
    "man_gesturing_no_medium-dark_skin_tone": "🙅🏾\u200d♂️",
    "man_gesturing_no_medium-light_skin_tone": "🙅🏼\u200d♂️",
    "man_gesturing_no_medium_skin_tone": "🙅🏽\u200d♂️",
    "man_gesturing_ok": "🙆\u200d♂️",
    "man_gesturing_ok_dark_skin_tone": "🙆🏿\u200d♂️",
    "man_gesturing_ok_light_skin_tone": "🙆🏻\u200d♂️",
    "man_gesturing_ok_medium-dark_skin_tone": "🙆🏾\u200d♂️",
    "man_gesturing_ok_medium-light_skin_tone": "🙆🏼\u200d♂️",
    "man_gesturing_ok_medium_skin_tone": "🙆🏽\u200d♂️",
    "man_getting_haircut": "💇\u200d♂️",
    "man_getting_haircut_dark_skin_tone": "💇🏿\u200d♂️",
    "man_getting_haircut_light_skin_tone": "💇🏻\u200d♂️",
    "man_getting_haircut_medium-dark_skin_tone": "💇🏾\u200d♂️",
    "man_getting_haircut_medium-light_skin_tone": "💇🏼\u200d♂️",
    "man_getting_haircut_medium_skin_tone": "💇🏽\u200d♂️",
    "man_getting_massage": "💆\u200d♂️",
    "man_getting_massage_dark_skin_tone": "💆🏿\u200d♂️",
    "man_getting_massage_light_skin_tone": "💆🏻\u200d♂️",
    "man_getting_massage_medium-dark_skin_tone": "💆🏾\u200d♂️",
    "man_getting_massage_medium-light_skin_tone": "💆🏼\u200d♂️",
    "man_getting_massage_medium_skin_tone": "💆🏽\u200d♂️",
    "man_golfing": "🏌️\u200d♂️",
    "man_golfing_dark_skin_tone": "🏌🏿\u200d♂️",
    "man_golfing_light_skin_tone": "🏌🏻\u200d♂️",
    "man_golfing_medium-dark_skin_tone": "🏌🏾\u200d♂️",
    "man_golfing_medium-light_skin_tone": "🏌🏼\u200d♂️",
    "man_golfing_medium_skin_tone": "🏌🏽\u200d♂️",
    "man_guard": "💂\u200d♂️",
    "man_guard_dark_skin_tone": "💂🏿\u200d♂️",
    "man_guard_light_skin_tone": "💂🏻\u200d♂️",
    "man_guard_medium-dark_skin_tone": "💂🏾\u200d♂️",
    "man_guard_medium-light_skin_tone": "💂🏼\u200d♂️",
    "man_guard_medium_skin_tone": "💂🏽\u200d♂️",
    "man_health_worker": "👨\u200d⚕️",
    "man_health_worker_dark_skin_tone": "👨🏿\u200d⚕️",
    "man_health_worker_light_skin_tone": "👨🏻\u200d⚕️",
    "man_health_worker_medium-dark_skin_tone": "👨🏾\u200d⚕️",
    "man_health_worker_medium-light_skin_tone": "👨🏼\u200d⚕️",
    "man_health_worker_medium_skin_tone": "👨🏽\u200d⚕️",
    "man_in_lotus_position": "🧘\u200d♂️",
    "man_in_lotus_position_dark_skin_tone": "🧘🏿\u200d♂️",
    "man_in_lotus_position_light_skin_tone": "🧘🏻\u200d♂️",
    "man_in_lotus_position_medium-dark_skin_tone": "🧘🏾\u200d♂️",
    "man_in_lotus_position_medium-light_skin_tone": "🧘🏼\u200d♂️",
    "man_in_lotus_position_medium_skin_tone": "🧘🏽\u200d♂️",
    "man_in_manual_wheelchair": "👨\u200d🦽",
    "man_in_motorized_wheelchair": "👨\u200d🦼",
    "man_in_steamy_room": "🧖\u200d♂️",
    "man_in_steamy_room_dark_skin_tone": "🧖🏿\u200d♂️",
    "man_in_steamy_room_light_skin_tone": "🧖🏻\u200d♂️",
    "man_in_steamy_room_medium-dark_skin_tone": "🧖🏾\u200d♂️",
    "man_in_steamy_room_medium-light_skin_tone": "🧖🏼\u200d♂️",
    "man_in_steamy_room_medium_skin_tone": "🧖🏽\u200d♂️",
    "man_in_suit_levitating": "🕴",
    "man_in_suit_levitating_dark_skin_tone": "🕴🏿",
    "man_in_suit_levitating_light_skin_tone": "🕴🏻",
    "man_in_suit_levitating_medium-dark_skin_tone": "🕴🏾",
    "man_in_suit_levitating_medium-light_skin_tone": "🕴🏼",
    "man_in_suit_levitating_medium_skin_tone": "🕴🏽",
    "man_in_tuxedo": "🤵",
    "man_in_tuxedo_dark_skin_tone": "🤵🏿",
    "man_in_tuxedo_light_skin_tone": "🤵🏻",
    "man_in_tuxedo_medium-dark_skin_tone": "🤵🏾",
    "man_in_tuxedo_medium-light_skin_tone": "🤵🏼",
    "man_in_tuxedo_medium_skin_tone": "🤵🏽",
    "man_judge": "👨\u200d⚖️",
    "man_judge_dark_skin_tone": "👨🏿\u200d⚖️",
    "man_judge_light_skin_tone": "👨🏻\u200d⚖️",
    "man_judge_medium-dark_skin_tone": "👨🏾\u200d⚖️",
    "man_judge_medium-light_skin_tone": "👨🏼\u200d⚖️",
    "man_judge_medium_skin_tone": "👨🏽\u200d⚖️",
    "man_juggling": "🤹\u200d♂️",
    "man_juggling_dark_skin_tone": "🤹🏿\u200d♂️",
    "man_juggling_light_skin_tone": "🤹🏻\u200d♂️",
    "man_juggling_medium-dark_skin_tone": "🤹🏾\u200d♂️",
    "man_juggling_medium-light_skin_tone": "🤹🏼\u200d♂️",
    "man_juggling_medium_skin_tone": "🤹🏽\u200d♂️",
    "man_lifting_weights": "🏋️\u200d♂️",
    "man_lifting_weights_dark_skin_tone": "🏋🏿\u200d♂️",
    "man_lifting_weights_light_skin_tone": "🏋🏻\u200d♂️",
    "man_lifting_weights_medium-dark_skin_tone": "🏋🏾\u200d♂️",
    "man_lifting_weights_medium-light_skin_tone": "🏋🏼\u200d♂️",
    "man_lifting_weights_medium_skin_tone": "🏋🏽\u200d♂️",
    "man_light_skin_tone": "👨🏻",
    "man_mage": "🧙\u200d♂️",
    "man_mage_dark_skin_tone": "🧙🏿\u200d♂️",
    "man_mage_light_skin_tone": "🧙🏻\u200d♂️",
    "man_mage_medium-dark_skin_tone": "🧙🏾\u200d♂️",
    "man_mage_medium-light_skin_tone": "🧙🏼\u200d♂️",
    "man_mage_medium_skin_tone": "🧙🏽\u200d♂️",
    "man_mechanic": "👨\u200d🔧",
    "man_mechanic_dark_skin_tone": "👨🏿\u200d🔧",
    "man_mechanic_light_skin_tone": "👨🏻\u200d🔧",
    "man_mechanic_medium-dark_skin_tone": "👨🏾\u200d🔧",
    "man_mechanic_medium-light_skin_tone": "👨🏼\u200d🔧",
    "man_mechanic_medium_skin_tone": "👨🏽\u200d🔧",
    "man_medium-dark_skin_tone": "👨🏾",
    "man_medium-light_skin_tone": "👨🏼",
    "man_medium_skin_tone": "👨🏽",
    "man_mountain_biking": "🚵\u200d♂️",
    "man_mountain_biking_dark_skin_tone": "🚵🏿\u200d♂️",
    "man_mountain_biking_light_skin_tone": "🚵🏻\u200d♂️",
    "man_mountain_biking_medium-dark_skin_tone": "🚵🏾\u200d♂️",
    "man_mountain_biking_medium-light_skin_tone": "🚵🏼\u200d♂️",
    "man_mountain_biking_medium_skin_tone": "🚵🏽\u200d♂️",
    "man_office_worker": "👨\u200d💼",
    "man_office_worker_dark_skin_tone": "👨🏿\u200d💼",
    "man_office_worker_light_skin_tone": "👨🏻\u200d💼",
    "man_office_worker_medium-dark_skin_tone": "👨🏾\u200d💼",
    "man_office_worker_medium-light_skin_tone": "👨🏼\u200d💼",
    "man_office_worker_medium_skin_tone": "👨🏽\u200d💼",
    "man_pilot": "👨\u200d✈️",
    "man_pilot_dark_skin_tone": "👨🏿\u200d✈️",
    "man_pilot_light_skin_tone": "👨🏻\u200d✈️",
    "man_pilot_medium-dark_skin_tone": "👨🏾\u200d✈️",
    "man_pilot_medium-light_skin_tone": "👨🏼\u200d✈️",
    "man_pilot_medium_skin_tone": "👨🏽\u200d✈️",
    "man_playing_handball": "🤾\u200d♂️",
    "man_playing_handball_dark_skin_tone": "🤾🏿\u200d♂️",
    "man_playing_handball_light_skin_tone": "🤾🏻\u200d♂️",
    "man_playing_handball_medium-dark_skin_tone": "🤾🏾\u200d♂️",
    "man_playing_handball_medium-light_skin_tone": "🤾🏼\u200d♂️",
    "man_playing_handball_medium_skin_tone": "🤾🏽\u200d♂️",
    "man_playing_water_polo": "🤽\u200d♂️",
    "man_playing_water_polo_dark_skin_tone": "🤽🏿\u200d♂️",
    "man_playing_water_polo_light_skin_tone": "🤽🏻\u200d♂️",
    "man_playing_water_polo_medium-dark_skin_tone": "🤽🏾\u200d♂️",
    "man_playing_water_polo_medium-light_skin_tone": "🤽🏼\u200d♂️",
    "man_playing_water_polo_medium_skin_tone": "🤽🏽\u200d♂️",
    "man_police_officer": "👮\u200d♂️",
    "man_police_officer_dark_skin_tone": "👮🏿\u200d♂️",
    "man_police_officer_light_skin_tone": "👮🏻\u200d♂️",
    "man_police_officer_medium-dark_skin_tone": "👮🏾\u200d♂️",
    "man_police_officer_medium-light_skin_tone": "👮🏼\u200d♂️",
    "man_police_officer_medium_skin_tone": "👮🏽\u200d♂️",
    "man_pouting": "🙎\u200d♂️",
    "man_pouting_dark_skin_tone": "🙎🏿\u200d♂️",
    "man_pouting_light_skin_tone": "🙎🏻\u200d♂️",
    "man_pouting_medium-dark_skin_tone": "🙎🏾\u200d♂️",
    "man_pouting_medium-light_skin_tone": "🙎🏼\u200d♂️",
    "man_pouting_medium_skin_tone": "🙎🏽\u200d♂️",
    "man_raising_hand": "🙋\u200d♂️",
    "man_raising_hand_dark_skin_tone": "🙋🏿\u200d♂️",
    "man_raising_hand_light_skin_tone": "🙋🏻\u200d♂️",
    "man_raising_hand_medium-dark_skin_tone": "🙋🏾\u200d♂️",
    "man_raising_hand_medium-light_skin_tone": "🙋🏼\u200d♂️",
    "man_raising_hand_medium_skin_tone": "🙋🏽\u200d♂️",
    "man_rowing_boat": "🚣\u200d♂️",
    "man_rowing_boat_dark_skin_tone": "🚣🏿\u200d♂️",
    "man_rowing_boat_light_skin_tone": "🚣🏻\u200d♂️",
    "man_rowing_boat_medium-dark_skin_tone": "🚣🏾\u200d♂️",
    "man_rowing_boat_medium-light_skin_tone": "🚣🏼\u200d♂️",
    "man_rowing_boat_medium_skin_tone": "🚣🏽\u200d♂️",
    "man_running": "🏃\u200d♂️",
    "man_running_dark_skin_tone": "🏃🏿\u200d♂️",
    "man_running_light_skin_tone": "🏃🏻\u200d♂️",
    "man_running_medium-dark_skin_tone": "🏃🏾\u200d♂️",
    "man_running_medium-light_skin_tone": "🏃🏼\u200d♂️",
    "man_running_medium_skin_tone": "🏃🏽\u200d♂️",
    "man_scientist": "👨\u200d🔬",
    "man_scientist_dark_skin_tone": "👨🏿\u200d🔬",
    "man_scientist_light_skin_tone": "👨🏻\u200d🔬",
    "man_scientist_medium-dark_skin_tone": "👨🏾\u200d🔬",
    "man_scientist_medium-light_skin_tone": "👨🏼\u200d🔬",
    "man_scientist_medium_skin_tone": "👨🏽\u200d🔬",
    "man_shrugging": "🤷\u200d♂️",
    "man_shrugging_dark_skin_tone": "🤷🏿\u200d♂️",
    "man_shrugging_light_skin_tone": "🤷🏻\u200d♂️",
    "man_shrugging_medium-dark_skin_tone": "🤷🏾\u200d♂️",
    "man_shrugging_medium-light_skin_tone": "🤷🏼\u200d♂️",
    "man_shrugging_medium_skin_tone": "🤷🏽\u200d♂️",
    "man_singer": "👨\u200d🎤",
    "man_singer_dark_skin_tone": "👨🏿\u200d🎤",
    "man_singer_light_skin_tone": "👨🏻\u200d🎤",
    "man_singer_medium-dark_skin_tone": "👨🏾\u200d🎤",
    "man_singer_medium-light_skin_tone": "👨🏼\u200d🎤",
    "man_singer_medium_skin_tone": "👨🏽\u200d🎤",
    "man_student": "👨\u200d🎓",
    "man_student_dark_skin_tone": "👨🏿\u200d🎓",
    "man_student_light_skin_tone": "👨🏻\u200d🎓",
    "man_student_medium-dark_skin_tone": "👨🏾\u200d🎓",
    "man_student_medium-light_skin_tone": "👨🏼\u200d🎓",
    "man_student_medium_skin_tone": "👨🏽\u200d🎓",
    "man_surfing": "🏄\u200d♂️",
    "man_surfing_dark_skin_tone": "🏄🏿\u200d♂️",
    "man_surfing_light_skin_tone": "🏄🏻\u200d♂️",
    "man_surfing_medium-dark_skin_tone": "🏄🏾\u200d♂️",
    "man_surfing_medium-light_skin_tone": "🏄🏼\u200d♂️",
    "man_surfing_medium_skin_tone": "🏄🏽\u200d♂️",
    "man_swimming": "🏊\u200d♂️",
    "man_swimming_dark_skin_tone": "🏊🏿\u200d♂️",
    "man_swimming_light_skin_tone": "🏊🏻\u200d♂️",
    "man_swimming_medium-dark_skin_tone": "🏊🏾\u200d♂️",
    "man_swimming_medium-light_skin_tone": "🏊🏼\u200d♂️",
    "man_swimming_medium_skin_tone": "🏊🏽\u200d♂️",
    "man_teacher": "👨\u200d🏫",
    "man_teacher_dark_skin_tone": "👨🏿\u200d🏫",
    "man_teacher_light_skin_tone": "👨🏻\u200d🏫",
    "man_teacher_medium-dark_skin_tone": "👨🏾\u200d🏫",
    "man_teacher_medium-light_skin_tone": "👨🏼\u200d🏫",
    "man_teacher_medium_skin_tone": "👨🏽\u200d🏫",
    "man_technologist": "👨\u200d💻",
    "man_technologist_dark_skin_tone": "👨🏿\u200d💻",
    "man_technologist_light_skin_tone": "👨🏻\u200d💻",
    "man_technologist_medium-dark_skin_tone": "👨🏾\u200d💻",
    "man_technologist_medium-light_skin_tone": "👨🏼\u200d💻",
    "man_technologist_medium_skin_tone": "👨🏽\u200d💻",
    "man_tipping_hand": "💁\u200d♂️",
    "man_tipping_hand_dark_skin_tone": "💁🏿\u200d♂️",
    "man_tipping_hand_light_skin_tone": "💁🏻\u200d♂️",
    "man_tipping_hand_medium-dark_skin_tone": "💁🏾\u200d♂️",
    "man_tipping_hand_medium-light_skin_tone": "💁🏼\u200d♂️",
    "man_tipping_hand_medium_skin_tone": "💁🏽\u200d♂️",
    "man_vampire": "🧛\u200d♂️",
    "man_vampire_dark_skin_tone": "🧛🏿\u200d♂️",
    "man_vampire_light_skin_tone": "🧛🏻\u200d♂️",
    "man_vampire_medium-dark_skin_tone": "🧛🏾\u200d♂️",
    "man_vampire_medium-light_skin_tone": "🧛🏼\u200d♂️",
    "man_vampire_medium_skin_tone": "🧛🏽\u200d♂️",
    "man_walking": "🚶\u200d♂️",
    "man_walking_dark_skin_tone": "🚶🏿\u200d♂️",
    "man_walking_light_skin_tone": "🚶🏻\u200d♂️",
    "man_walking_medium-dark_skin_tone": "🚶🏾\u200d♂️",
    "man_walking_medium-light_skin_tone": "🚶🏼\u200d♂️",
    "man_walking_medium_skin_tone": "🚶🏽\u200d♂️",
    "man_wearing_turban": "👳\u200d♂️",
    "man_wearing_turban_dark_skin_tone": "👳🏿\u200d♂️",
    "man_wearing_turban_light_skin_tone": "👳🏻\u200d♂️",
    "man_wearing_turban_medium-dark_skin_tone": "👳🏾\u200d♂️",
    "man_wearing_turban_medium-light_skin_tone": "👳🏼\u200d♂️",
    "man_wearing_turban_medium_skin_tone": "👳🏽\u200d♂️",
    "man_with_probing_cane": "👨\u200d🦯",
    "man_with_chinese_cap": "👲",
    "man_with_chinese_cap_dark_skin_tone": "👲🏿",
    "man_with_chinese_cap_light_skin_tone": "👲🏻",
    "man_with_chinese_cap_medium-dark_skin_tone": "👲🏾",
    "man_with_chinese_cap_medium-light_skin_tone": "👲🏼",
    "man_with_chinese_cap_medium_skin_tone": "👲🏽",
    "man_zombie": "🧟\u200d♂️",
    "mango": "🥭",
    "mantelpiece_clock": "🕰",
    "manual_wheelchair": "🦽",
    "man’s_shoe": "👞",
    "map_of_japan": "🗾",
    "maple_leaf": "🍁",
    "martial_arts_uniform": "🥋",
    "mate": "🧉",
    "meat_on_bone": "🍖",
    "mechanical_arm": "🦾",
    "mechanical_leg": "🦿",
    "medical_symbol": "⚕",
    "megaphone": "📣",
    "melon": "🍈",
    "memo": "📝",
    "men_with_bunny_ears": "👯\u200d♂️",
    "men_wrestling": "🤼\u200d♂️",
    "menorah": "🕎",
    "men’s_room": "🚹",
    "mermaid": "🧜\u200d♀️",
    "mermaid_dark_skin_tone": "🧜🏿\u200d♀️",
    "mermaid_light_skin_tone": "🧜🏻\u200d♀️",
    "mermaid_medium-dark_skin_tone": "🧜🏾\u200d♀️",
    "mermaid_medium-light_skin_tone": "🧜🏼\u200d♀️",
    "mermaid_medium_skin_tone": "🧜🏽\u200d♀️",
    "merman": "🧜\u200d♂️",
    "merman_dark_skin_tone": "🧜🏿\u200d♂️",
    "merman_light_skin_tone": "🧜🏻\u200d♂️",
    "merman_medium-dark_skin_tone": "🧜🏾\u200d♂️",
    "merman_medium-light_skin_tone": "🧜🏼\u200d♂️",
    "merman_medium_skin_tone": "🧜🏽\u200d♂️",
    "merperson": "🧜",
    "merperson_dark_skin_tone": "🧜🏿",
    "merperson_light_skin_tone": "🧜🏻",
    "merperson_medium-dark_skin_tone": "🧜🏾",
    "merperson_medium-light_skin_tone": "🧜🏼",
    "merperson_medium_skin_tone": "🧜🏽",
    "metro": "🚇",
    "microbe": "🦠",
    "microphone": "🎤",
    "microscope": "🔬",
    "middle_finger": "🖕",
    "middle_finger_dark_skin_tone": "🖕🏿",
    "middle_finger_light_skin_tone": "🖕🏻",
    "middle_finger_medium-dark_skin_tone": "🖕🏾",
    "middle_finger_medium-light_skin_tone": "🖕🏼",
    "middle_finger_medium_skin_tone": "🖕🏽",
    "military_medal": "🎖",
    "milky_way": "🌌",
    "minibus": "🚐",
    "moai": "🗿",
    "mobile_phone": "📱",
    "mobile_phone_off": "📴",
    "mobile_phone_with_arrow": "📲",
    "money-mouth_face": "🤑",
    "money_bag": "💰",
    "money_with_wings": "💸",
    "monkey": "🐒",
    "monkey_face": "🐵",
    "monorail": "🚝",
    "moon_cake": "🥮",
    "moon_viewing_ceremony": "🎑",
    "mosque": "🕌",
    "mosquito": "🦟",
    "motor_boat": "🛥",
    "motor_scooter": "🛵",
    "motorcycle": "🏍",
    "motorized_wheelchair": "🦼",
    "motorway": "🛣",
    "mount_fuji": "🗻",
    "mountain": "⛰",
    "mountain_cableway": "🚠",
    "mountain_railway": "🚞",
    "mouse": "🐭",
    "mouse_face": "🐭",
    "mouth": "👄",
    "movie_camera": "🎥",
    "mushroom": "🍄",
    "musical_keyboard": "🎹",
    "musical_note": "🎵",
    "musical_notes": "🎶",
    "musical_score": "🎼",
    "muted_speaker": "🔇",
    "nail_polish": "💅",
    "nail_polish_dark_skin_tone": "💅🏿",
    "nail_polish_light_skin_tone": "💅🏻",
    "nail_polish_medium-dark_skin_tone": "💅🏾",
    "nail_polish_medium-light_skin_tone": "💅🏼",
    "nail_polish_medium_skin_tone": "💅🏽",
    "name_badge": "📛",
    "national_park": "🏞",
    "nauseated_face": "🤢",
    "nazar_amulet": "🧿",
    "necktie": "👔",
    "nerd_face": "🤓",
    "neutral_face": "😐",
    "new_moon": "🌑",
    "new_moon_face": "🌚",
    "newspaper": "📰",
    "next_track_button": "⏭",
    "night_with_stars": "🌃",
    "nine-thirty": "🕤",
    "nine_o’clock": "🕘",
    "no_bicycles": "🚳",
    "no_entry": "⛔",
    "no_littering": "🚯",
    "no_mobile_phones": "📵",
    "no_one_under_eighteen": "🔞",
    "no_pedestrians": "🚷",
    "no_smoking": "🚭",
    "non-potable_water": "🚱",
    "nose": "👃",
    "nose_dark_skin_tone": "👃🏿",
    "nose_light_skin_tone": "👃🏻",
    "nose_medium-dark_skin_tone": "👃🏾",
    "nose_medium-light_skin_tone": "👃🏼",
    "nose_medium_skin_tone": "👃🏽",
    "notebook": "📓",
    "notebook_with_decorative_cover": "📔",
    "nut_and_bolt": "🔩",
    "octopus": "🐙",
    "oden": "🍢",
    "office_building": "🏢",
    "ogre": "👹",
    "oil_drum": "🛢",
    "old_key": "🗝",
    "old_man": "👴",
    "old_man_dark_skin_tone": "👴🏿",
    "old_man_light_skin_tone": "👴🏻",
    "old_man_medium-dark_skin_tone": "👴🏾",
    "old_man_medium-light_skin_tone": "👴🏼",
    "old_man_medium_skin_tone": "👴🏽",
    "old_woman": "👵",
    "old_woman_dark_skin_tone": "👵🏿",
    "old_woman_light_skin_tone": "👵🏻",
    "old_woman_medium-dark_skin_tone": "👵🏾",
    "old_woman_medium-light_skin_tone": "👵🏼",
    "old_woman_medium_skin_tone": "👵🏽",
    "older_adult": "🧓",
    "older_adult_dark_skin_tone": "🧓🏿",
    "older_adult_light_skin_tone": "🧓🏻",
    "older_adult_medium-dark_skin_tone": "🧓🏾",
    "older_adult_medium-light_skin_tone": "🧓🏼",
    "older_adult_medium_skin_tone": "🧓🏽",
    "om": "🕉",
    "oncoming_automobile": "🚘",
    "oncoming_bus": "🚍",
    "oncoming_fist": "👊",
    "oncoming_fist_dark_skin_tone": "👊🏿",
    "oncoming_fist_light_skin_tone": "👊🏻",
    "oncoming_fist_medium-dark_skin_tone": "👊🏾",
    "oncoming_fist_medium-light_skin_tone": "👊🏼",
    "oncoming_fist_medium_skin_tone": "👊🏽",
    "oncoming_police_car": "🚔",
    "oncoming_taxi": "🚖",
    "one-piece_swimsuit": "🩱",
    "one-thirty": "🕜",
    "one_o’clock": "🕐",
    "onion": "🧅",
    "open_book": "📖",
    "open_file_folder": "📂",
    "open_hands": "👐",
    "open_hands_dark_skin_tone": "👐🏿",
    "open_hands_light_skin_tone": "👐🏻",
    "open_hands_medium-dark_skin_tone": "👐🏾",
    "open_hands_medium-light_skin_tone": "👐🏼",
    "open_hands_medium_skin_tone": "👐🏽",
    "open_mailbox_with_lowered_flag": "📭",
    "open_mailbox_with_raised_flag": "📬",
    "optical_disk": "💿",
    "orange_book": "📙",
    "orange_circle": "🟠",
    "orange_heart": "🧡",
    "orange_square": "🟧",
    "orangutan": "🦧",
    "orthodox_cross": "☦",
    "otter": "🦦",
    "outbox_tray": "📤",
    "owl": "🦉",
    "ox": "🐂",
    "oyster": "🦪",
    "package": "📦",
    "page_facing_up": "📄",
    "page_with_curl": "📃",
    "pager": "📟",
    "paintbrush": "🖌",
    "palm_tree": "🌴",
    "palms_up_together": "🤲",
    "palms_up_together_dark_skin_tone": "🤲🏿",
    "palms_up_together_light_skin_tone": "🤲🏻",
    "palms_up_together_medium-dark_skin_tone": "🤲🏾",
    "palms_up_together_medium-light_skin_tone": "🤲🏼",
    "palms_up_together_medium_skin_tone": "🤲🏽",
    "pancakes": "🥞",
    "panda_face": "🐼",
    "paperclip": "📎",
    "parrot": "🦜",
    "part_alternation_mark": "〽",
    "party_popper": "🎉",
    "partying_face": "🥳",
    "passenger_ship": "🛳",
    "passport_control": "🛂",
    "pause_button": "⏸",
    "paw_prints": "🐾",
    "peace_symbol": "☮",
    "peach": "🍑",
    "peacock": "🦚",
    "peanuts": "🥜",
    "pear": "🍐",
    "pen": "🖊",
    "pencil": "📝",
    "penguin": "🐧",
    "pensive_face": "😔",
    "people_holding_hands": "🧑\u200d🤝\u200d🧑",
    "people_with_bunny_ears": "👯",
    "people_wrestling": "🤼",
    "performing_arts": "🎭",
    "persevering_face": "😣",
    "person_biking": "🚴",
    "person_biking_dark_skin_tone": "🚴🏿",
    "person_biking_light_skin_tone": "🚴🏻",
    "person_biking_medium-dark_skin_tone": "🚴🏾",
    "person_biking_medium-light_skin_tone": "🚴🏼",
    "person_biking_medium_skin_tone": "🚴🏽",
    "person_bouncing_ball": "⛹",
    "person_bouncing_ball_dark_skin_tone": "⛹🏿",
    "person_bouncing_ball_light_skin_tone": "⛹🏻",
    "person_bouncing_ball_medium-dark_skin_tone": "⛹🏾",
    "person_bouncing_ball_medium-light_skin_tone": "⛹🏼",
    "person_bouncing_ball_medium_skin_tone": "⛹🏽",
    "person_bowing": "🙇",
    "person_bowing_dark_skin_tone": "🙇🏿",
    "person_bowing_light_skin_tone": "🙇🏻",
    "person_bowing_medium-dark_skin_tone": "🙇🏾",
    "person_bowing_medium-light_skin_tone": "🙇🏼",
    "person_bowing_medium_skin_tone": "🙇🏽",
    "person_cartwheeling": "🤸",
    "person_cartwheeling_dark_skin_tone": "🤸🏿",
    "person_cartwheeling_light_skin_tone": "🤸🏻",
    "person_cartwheeling_medium-dark_skin_tone": "🤸🏾",
    "person_cartwheeling_medium-light_skin_tone": "🤸🏼",
    "person_cartwheeling_medium_skin_tone": "🤸🏽",
    "person_climbing": "🧗",
    "person_climbing_dark_skin_tone": "🧗🏿",
    "person_climbing_light_skin_tone": "🧗🏻",
    "person_climbing_medium-dark_skin_tone": "🧗🏾",
    "person_climbing_medium-light_skin_tone": "🧗🏼",
    "person_climbing_medium_skin_tone": "🧗🏽",
    "person_facepalming": "🤦",
    "person_facepalming_dark_skin_tone": "🤦🏿",
    "person_facepalming_light_skin_tone": "🤦🏻",
    "person_facepalming_medium-dark_skin_tone": "🤦🏾",
    "person_facepalming_medium-light_skin_tone": "🤦🏼",
    "person_facepalming_medium_skin_tone": "🤦🏽",
    "person_fencing": "🤺",
    "person_frowning": "🙍",
    "person_frowning_dark_skin_tone": "🙍🏿",
    "person_frowning_light_skin_tone": "🙍🏻",
    "person_frowning_medium-dark_skin_tone": "🙍🏾",
    "person_frowning_medium-light_skin_tone": "🙍🏼",
    "person_frowning_medium_skin_tone": "🙍🏽",
    "person_gesturing_no": "🙅",
    "person_gesturing_no_dark_skin_tone": "🙅🏿",
    "person_gesturing_no_light_skin_tone": "🙅🏻",
    "person_gesturing_no_medium-dark_skin_tone": "🙅🏾",
    "person_gesturing_no_medium-light_skin_tone": "🙅🏼",
    "person_gesturing_no_medium_skin_tone": "🙅🏽",
    "person_gesturing_ok": "🙆",
    "person_gesturing_ok_dark_skin_tone": "🙆🏿",
    "person_gesturing_ok_light_skin_tone": "🙆🏻",
    "person_gesturing_ok_medium-dark_skin_tone": "🙆🏾",
    "person_gesturing_ok_medium-light_skin_tone": "🙆🏼",
    "person_gesturing_ok_medium_skin_tone": "🙆🏽",
    "person_getting_haircut": "💇",
    "person_getting_haircut_dark_skin_tone": "💇🏿",
    "person_getting_haircut_light_skin_tone": "💇🏻",
    "person_getting_haircut_medium-dark_skin_tone": "💇🏾",
    "person_getting_haircut_medium-light_skin_tone": "💇🏼",
    "person_getting_haircut_medium_skin_tone": "💇🏽",
    "person_getting_massage": "💆",
    "person_getting_massage_dark_skin_tone": "💆🏿",
    "person_getting_massage_light_skin_tone": "💆🏻",
    "person_getting_massage_medium-dark_skin_tone": "💆🏾",
    "person_getting_massage_medium-light_skin_tone": "💆🏼",
    "person_getting_massage_medium_skin_tone": "💆🏽",
    "person_golfing": "🏌",
    "person_golfing_dark_skin_tone": "🏌🏿",
    "person_golfing_light_skin_tone": "🏌🏻",
    "person_golfing_medium-dark_skin_tone": "🏌🏾",
    "person_golfing_medium-light_skin_tone": "🏌🏼",
    "person_golfing_medium_skin_tone": "🏌🏽",
    "person_in_bed": "🛌",
    "person_in_bed_dark_skin_tone": "🛌🏿",
    "person_in_bed_light_skin_tone": "🛌🏻",
    "person_in_bed_medium-dark_skin_tone": "🛌🏾",
    "person_in_bed_medium-light_skin_tone": "🛌🏼",
    "person_in_bed_medium_skin_tone": "🛌🏽",
    "person_in_lotus_position": "🧘",
    "person_in_lotus_position_dark_skin_tone": "🧘🏿",
    "person_in_lotus_position_light_skin_tone": "🧘🏻",
    "person_in_lotus_position_medium-dark_skin_tone": "🧘🏾",
    "person_in_lotus_position_medium-light_skin_tone": "🧘🏼",
    "person_in_lotus_position_medium_skin_tone": "🧘🏽",
    "person_in_steamy_room": "🧖",
    "person_in_steamy_room_dark_skin_tone": "🧖🏿",
    "person_in_steamy_room_light_skin_tone": "🧖🏻",
    "person_in_steamy_room_medium-dark_skin_tone": "🧖🏾",
    "person_in_steamy_room_medium-light_skin_tone": "🧖🏼",
    "person_in_steamy_room_medium_skin_tone": "🧖🏽",
    "person_juggling": "🤹",
    "person_juggling_dark_skin_tone": "🤹🏿",
    "person_juggling_light_skin_tone": "🤹🏻",
    "person_juggling_medium-dark_skin_tone": "🤹🏾",
    "person_juggling_medium-light_skin_tone": "🤹🏼",
    "person_juggling_medium_skin_tone": "🤹🏽",
    "person_kneeling": "🧎",
    "person_lifting_weights": "🏋",
    "person_lifting_weights_dark_skin_tone": "🏋🏿",
    "person_lifting_weights_light_skin_tone": "🏋🏻",
    "person_lifting_weights_medium-dark_skin_tone": "🏋🏾",
    "person_lifting_weights_medium-light_skin_tone": "🏋🏼",
    "person_lifting_weights_medium_skin_tone": "🏋🏽",
    "person_mountain_biking": "🚵",
    "person_mountain_biking_dark_skin_tone": "🚵🏿",
    "person_mountain_biking_light_skin_tone": "🚵🏻",
    "person_mountain_biking_medium-dark_skin_tone": "🚵🏾",
    "person_mountain_biking_medium-light_skin_tone": "🚵🏼",
    "person_mountain_biking_medium_skin_tone": "🚵🏽",
    "person_playing_handball": "🤾",
    "person_playing_handball_dark_skin_tone": "🤾🏿",
    "person_playing_handball_light_skin_tone": "🤾🏻",
    "person_playing_handball_medium-dark_skin_tone": "🤾🏾",
    "person_playing_handball_medium-light_skin_tone": "🤾🏼",
    "person_playing_handball_medium_skin_tone": "🤾🏽",
    "person_playing_water_polo": "🤽",
    "person_playing_water_polo_dark_skin_tone": "🤽🏿",
    "person_playing_water_polo_light_skin_tone": "🤽🏻",
    "person_playing_water_polo_medium-dark_skin_tone": "🤽🏾",
    "person_playing_water_polo_medium-light_skin_tone": "🤽🏼",
    "person_playing_water_polo_medium_skin_tone": "🤽🏽",
    "person_pouting": "🙎",
    "person_pouting_dark_skin_tone": "🙎🏿",
    "person_pouting_light_skin_tone": "🙎🏻",
    "person_pouting_medium-dark_skin_tone": "🙎🏾",
    "person_pouting_medium-light_skin_tone": "🙎🏼",
    "person_pouting_medium_skin_tone": "🙎🏽",
    "person_raising_hand": "🙋",
    "person_raising_hand_dark_skin_tone": "🙋🏿",
    "person_raising_hand_light_skin_tone": "🙋🏻",
    "person_raising_hand_medium-dark_skin_tone": "🙋🏾",
    "person_raising_hand_medium-light_skin_tone": "🙋🏼",
    "person_raising_hand_medium_skin_tone": "🙋🏽",
    "person_rowing_boat": "🚣",
    "person_rowing_boat_dark_skin_tone": "🚣🏿",
    "person_rowing_boat_light_skin_tone": "🚣🏻",
    "person_rowing_boat_medium-dark_skin_tone": "🚣🏾",
    "person_rowing_boat_medium-light_skin_tone": "🚣🏼",
    "person_rowing_boat_medium_skin_tone": "🚣🏽",
    "person_running": "🏃",
    "person_running_dark_skin_tone": "🏃🏿",
    "person_running_light_skin_tone": "🏃🏻",
    "person_running_medium-dark_skin_tone": "🏃🏾",
    "person_running_medium-light_skin_tone": "🏃🏼",
    "person_running_medium_skin_tone": "🏃🏽",
    "person_shrugging": "🤷",
    "person_shrugging_dark_skin_tone": "🤷🏿",
    "person_shrugging_light_skin_tone": "🤷🏻",
    "person_shrugging_medium-dark_skin_tone": "🤷🏾",
    "person_shrugging_medium-light_skin_tone": "🤷🏼",
    "person_shrugging_medium_skin_tone": "🤷🏽",
    "person_standing": "🧍",
    "person_surfing": "🏄",
    "person_surfing_dark_skin_tone": "🏄🏿",
    "person_surfing_light_skin_tone": "🏄🏻",
    "person_surfing_medium-dark_skin_tone": "🏄🏾",
    "person_surfing_medium-light_skin_tone": "🏄🏼",
    "person_surfing_medium_skin_tone": "🏄🏽",
    "person_swimming": "🏊",
    "person_swimming_dark_skin_tone": "🏊🏿",
    "person_swimming_light_skin_tone": "🏊🏻",
    "person_swimming_medium-dark_skin_tone": "🏊🏾",
    "person_swimming_medium-light_skin_tone": "🏊🏼",
    "person_swimming_medium_skin_tone": "🏊🏽",
    "person_taking_bath": "🛀",
    "person_taking_bath_dark_skin_tone": "🛀🏿",
    "person_taking_bath_light_skin_tone": "🛀🏻",
    "person_taking_bath_medium-dark_skin_tone": "🛀🏾",
    "person_taking_bath_medium-light_skin_tone": "🛀🏼",
    "person_taking_bath_medium_skin_tone": "🛀🏽",
    "person_tipping_hand": "💁",
    "person_tipping_hand_dark_skin_tone": "💁🏿",
    "person_tipping_hand_light_skin_tone": "💁🏻",
    "person_tipping_hand_medium-dark_skin_tone": "💁🏾",
    "person_tipping_hand_medium-light_skin_tone": "💁🏼",
    "person_tipping_hand_medium_skin_tone": "💁🏽",
    "person_walking": "🚶",
    "person_walking_dark_skin_tone": "🚶🏿",
    "person_walking_light_skin_tone": "🚶🏻",
    "person_walking_medium-dark_skin_tone": "🚶🏾",
    "person_walking_medium-light_skin_tone": "🚶🏼",
    "person_walking_medium_skin_tone": "🚶🏽",
    "person_wearing_turban": "👳",
    "person_wearing_turban_dark_skin_tone": "👳🏿",
    "person_wearing_turban_light_skin_tone": "👳🏻",
    "person_wearing_turban_medium-dark_skin_tone": "👳🏾",
    "person_wearing_turban_medium-light_skin_tone": "👳🏼",
    "person_wearing_turban_medium_skin_tone": "👳🏽",
    "petri_dish": "🧫",
    "pick": "⛏",
    "pie": "🥧",
    "pig": "🐷",
    "pig_face": "🐷",
    "pig_nose": "🐽",
    "pile_of_poo": "💩",
    "pill": "💊",
    "pinching_hand": "🤏",
    "pine_decoration": "🎍",
    "pineapple": "🍍",
    "ping_pong": "🏓",
    "pirate_flag": "🏴\u200d☠️",
    "pistol": "🔫",
    "pizza": "🍕",
    "place_of_worship": "🛐",
    "play_button": "▶",
    "play_or_pause_button": "⏯",
    "pleading_face": "🥺",
    "police_car": "🚓",
    "police_car_light": "🚨",
    "police_officer": "👮",
    "police_officer_dark_skin_tone": "👮🏿",
    "police_officer_light_skin_tone": "👮🏻",
    "police_officer_medium-dark_skin_tone": "👮🏾",
    "police_officer_medium-light_skin_tone": "👮🏼",
    "police_officer_medium_skin_tone": "👮🏽",
    "poodle": "🐩",
    "pool_8_ball": "🎱",
    "popcorn": "🍿",
    "post_office": "🏣",
    "postal_horn": "📯",
    "postbox": "📮",
    "pot_of_food": "🍲",
    "potable_water": "🚰",
    "potato": "🥔",
    "poultry_leg": "🍗",
    "pound_banknote": "💷",
    "pouting_cat_face": "😾",
    "pouting_face": "😡",
    "prayer_beads": "📿",
    "pregnant_woman": "🤰",
    "pregnant_woman_dark_skin_tone": "🤰🏿",
    "pregnant_woman_light_skin_tone": "🤰🏻",
    "pregnant_woman_medium-dark_skin_tone": "🤰🏾",
    "pregnant_woman_medium-light_skin_tone": "🤰🏼",
    "pregnant_woman_medium_skin_tone": "🤰🏽",
    "pretzel": "🥨",
    "probing_cane": "🦯",
    "prince": "🤴",
    "prince_dark_skin_tone": "🤴🏿",
    "prince_light_skin_tone": "🤴🏻",
    "prince_medium-dark_skin_tone": "🤴🏾",
    "prince_medium-light_skin_tone": "🤴🏼",
    "prince_medium_skin_tone": "🤴🏽",
    "princess": "👸",
    "princess_dark_skin_tone": "👸🏿",
    "princess_light_skin_tone": "👸🏻",
    "princess_medium-dark_skin_tone": "👸🏾",
    "princess_medium-light_skin_tone": "👸🏼",
    "princess_medium_skin_tone": "👸🏽",
    "printer": "🖨",
    "prohibited": "🚫",
    "purple_circle": "🟣",
    "purple_heart": "💜",
    "purple_square": "🟪",
    "purse": "👛",
    "pushpin": "📌",
    "question_mark": "❓",
    "rabbit": "🐰",
    "rabbit_face": "🐰",
    "raccoon": "🦝",
    "racing_car": "🏎",
    "radio": "📻",
    "radio_button": "🔘",
    "radioactive": "☢",
    "railway_car": "🚃",
    "railway_track": "🛤",
    "rainbow": "🌈",
    "rainbow_flag": "🏳️\u200d🌈",
    "raised_back_of_hand": "🤚",
    "raised_back_of_hand_dark_skin_tone": "🤚🏿",
    "raised_back_of_hand_light_skin_tone": "🤚🏻",
    "raised_back_of_hand_medium-dark_skin_tone": "🤚🏾",
    "raised_back_of_hand_medium-light_skin_tone": "🤚🏼",
    "raised_back_of_hand_medium_skin_tone": "🤚🏽",
    "raised_fist": "✊",
    "raised_fist_dark_skin_tone": "✊🏿",
    "raised_fist_light_skin_tone": "✊🏻",
    "raised_fist_medium-dark_skin_tone": "✊🏾",
    "raised_fist_medium-light_skin_tone": "✊🏼",
    "raised_fist_medium_skin_tone": "✊🏽",
    "raised_hand": "✋",
    "raised_hand_dark_skin_tone": "✋🏿",
    "raised_hand_light_skin_tone": "✋🏻",
    "raised_hand_medium-dark_skin_tone": "✋🏾",
    "raised_hand_medium-light_skin_tone": "✋🏼",
    "raised_hand_medium_skin_tone": "✋🏽",
    "raising_hands": "🙌",
    "raising_hands_dark_skin_tone": "🙌🏿",
    "raising_hands_light_skin_tone": "🙌🏻",
    "raising_hands_medium-dark_skin_tone": "🙌🏾",
    "raising_hands_medium-light_skin_tone": "🙌🏼",
    "raising_hands_medium_skin_tone": "🙌🏽",
    "ram": "🐏",
    "rat": "🐀",
    "razor": "🪒",
    "ringed_planet": "🪐",
    "receipt": "🧾",
    "record_button": "⏺",
    "recycling_symbol": "♻",
    "red_apple": "🍎",
    "red_circle": "🔴",
    "red_envelope": "🧧",
    "red_hair": "🦰",
    "red-haired_man": "👨\u200d🦰",
    "red-haired_woman": "👩\u200d🦰",
    "red_heart": "❤",
    "red_paper_lantern": "🏮",
    "red_square": "🟥",
    "red_triangle_pointed_down": "🔻",
    "red_triangle_pointed_up": "🔺",
    "registered": "®",
    "relieved_face": "😌",
    "reminder_ribbon": "🎗",
    "repeat_button": "🔁",
    "repeat_single_button": "🔂",
    "rescue_worker’s_helmet": "⛑",
    "restroom": "🚻",
    "reverse_button": "◀",
    "revolving_hearts": "💞",
    "rhinoceros": "🦏",
    "ribbon": "🎀",
    "rice_ball": "🍙",
    "rice_cracker": "🍘",
    "right-facing_fist": "🤜",
    "right-facing_fist_dark_skin_tone": "🤜🏿",
    "right-facing_fist_light_skin_tone": "🤜🏻",
    "right-facing_fist_medium-dark_skin_tone": "🤜🏾",
    "right-facing_fist_medium-light_skin_tone": "🤜🏼",
    "right-facing_fist_medium_skin_tone": "🤜🏽",
    "right_anger_bubble": "🗯",
    "right_arrow": "➡",
    "right_arrow_curving_down": "⤵",
    "right_arrow_curving_left": "↩",
    "right_arrow_curving_up": "⤴",
    "ring": "💍",
    "roasted_sweet_potato": "🍠",
    "robot_face": "🤖",
    "rocket": "🚀",
    "roll_of_paper": "🧻",
    "rolled-up_newspaper": "🗞",
    "roller_coaster": "🎢",
    "rolling_on_the_floor_laughing": "🤣",
    "rooster": "🐓",
    "rose": "🌹",
    "rosette": "🏵",
    "round_pushpin": "📍",
    "rugby_football": "🏉",
    "running_shirt": "🎽",
    "running_shoe": "👟",
    "sad_but_relieved_face": "😥",
    "safety_pin": "🧷",
    "safety_vest": "🦺",
    "salt": "🧂",
    "sailboat": "⛵",
    "sake": "🍶",
    "sandwich": "🥪",
    "sari": "🥻",
    "satellite": "📡",
    "satellite_antenna": "📡",
    "sauropod": "🦕",
    "saxophone": "🎷",
    "scarf": "🧣",
    "school": "🏫",
    "school_backpack": "🎒",
    "scissors": "✂",
    "scorpion": "🦂",
    "scroll": "📜",
    "seat": "💺",
    "see-no-evil_monkey": "🙈",
    "seedling": "🌱",
    "selfie": "🤳",
    "selfie_dark_skin_tone": "🤳🏿",
    "selfie_light_skin_tone": "🤳🏻",
    "selfie_medium-dark_skin_tone": "🤳🏾",
    "selfie_medium-light_skin_tone": "🤳🏼",
    "selfie_medium_skin_tone": "🤳🏽",
    "service_dog": "🐕\u200d🦺",
    "seven-thirty": "🕢",
    "seven_o’clock": "🕖",
    "shallow_pan_of_food": "🥘",
    "shamrock": "☘",
    "shark": "🦈",
    "shaved_ice": "🍧",
    "sheaf_of_rice": "🌾",
    "shield": "🛡",
    "shinto_shrine": "⛩",
    "ship": "🚢",
    "shooting_star": "🌠",
    "shopping_bags": "🛍",
    "shopping_cart": "🛒",
    "shortcake": "🍰",
    "shorts": "🩳",
    "shower": "🚿",
    "shrimp": "🦐",
    "shuffle_tracks_button": "🔀",
    "shushing_face": "🤫",
    "sign_of_the_horns": "🤘",
    "sign_of_the_horns_dark_skin_tone": "🤘🏿",
    "sign_of_the_horns_light_skin_tone": "🤘🏻",
    "sign_of_the_horns_medium-dark_skin_tone": "🤘🏾",
    "sign_of_the_horns_medium-light_skin_tone": "🤘🏼",
    "sign_of_the_horns_medium_skin_tone": "🤘🏽",
    "six-thirty": "🕡",
    "six_o’clock": "🕕",
    "skateboard": "🛹",
    "skier": "⛷",
    "skis": "🎿",
    "skull": "💀",
    "skull_and_crossbones": "☠",
    "skunk": "🦨",
    "sled": "🛷",
    "sleeping_face": "😴",
    "sleepy_face": "😪",
    "slightly_frowning_face": "🙁",
    "slightly_smiling_face": "🙂",
    "slot_machine": "🎰",
    "sloth": "🦥",
    "small_airplane": "🛩",
    "small_blue_diamond": "🔹",
    "small_orange_diamond": "🔸",
    "smiling_cat_face_with_heart-eyes": "😻",
    "smiling_face": "☺",
    "smiling_face_with_halo": "😇",
    "smiling_face_with_3_hearts": "🥰",
    "smiling_face_with_heart-eyes": "😍",
    "smiling_face_with_horns": "😈",
    "smiling_face_with_smiling_eyes": "😊",
    "smiling_face_with_sunglasses": "😎",
    "smirking_face": "😏",
    "snail": "🐌",
    "snake": "🐍",
    "sneezing_face": "🤧",
    "snow-capped_mountain": "🏔",
    "snowboarder": "🏂",
    "snowboarder_dark_skin_tone": "🏂🏿",
    "snowboarder_light_skin_tone": "🏂🏻",
    "snowboarder_medium-dark_skin_tone": "🏂🏾",
    "snowboarder_medium-light_skin_tone": "🏂🏼",
    "snowboarder_medium_skin_tone": "🏂🏽",
    "snowflake": "❄",
    "snowman": "☃",
    "snowman_without_snow": "⛄",
    "soap": "🧼",
    "soccer_ball": "⚽",
    "socks": "🧦",
    "softball": "🥎",
    "soft_ice_cream": "🍦",
    "spade_suit": "♠",
    "spaghetti": "🍝",
    "sparkle": "❇",
    "sparkler": "🎇",
    "sparkles": "✨",
    "sparkling_heart": "💖",
    "speak-no-evil_monkey": "🙊",
    "speaker_high_volume": "🔊",
    "speaker_low_volume": "🔈",
    "speaker_medium_volume": "🔉",
    "speaking_head": "🗣",
    "speech_balloon": "💬",
    "speedboat": "🚤",
    "spider": "🕷",
    "spider_web": "🕸",
    "spiral_calendar": "🗓",
    "spiral_notepad": "🗒",
    "spiral_shell": "🐚",
    "spoon": "🥄",
    "sponge": "🧽",
    "sport_utility_vehicle": "🚙",
    "sports_medal": "🏅",
    "spouting_whale": "🐳",
    "squid": "🦑",
    "squinting_face_with_tongue": "😝",
    "stadium": "🏟",
    "star-struck": "🤩",
    "star_and_crescent": "☪",
    "star_of_david": "✡",
    "station": "🚉",
    "steaming_bowl": "🍜",
    "stethoscope": "🩺",
    "stop_button": "⏹",
    "stop_sign": "🛑",
    "stopwatch": "⏱",
    "straight_ruler": "📏",
    "strawberry": "🍓",
    "studio_microphone": "🎙",
    "stuffed_flatbread": "🥙",
    "sun": "☀",
    "sun_behind_cloud": "⛅",
    "sun_behind_large_cloud": "🌥",
    "sun_behind_rain_cloud": "🌦",
    "sun_behind_small_cloud": "🌤",
    "sun_with_face": "🌞",
    "sunflower": "🌻",
    "sunglasses": "😎",
    "sunrise": "🌅",
    "sunrise_over_mountains": "🌄",
    "sunset": "🌇",
    "superhero": "🦸",
    "supervillain": "🦹",
    "sushi": "🍣",
    "suspension_railway": "🚟",
    "swan": "🦢",
    "sweat_droplets": "💦",
    "synagogue": "🕍",
    "syringe": "💉",
    "t-shirt": "👕",
    "taco": "🌮",
    "takeout_box": "🥡",
    "tanabata_tree": "🎋",
    "tangerine": "🍊",
    "taxi": "🚕",
    "teacup_without_handle": "🍵",
    "tear-off_calendar": "📆",
    "teddy_bear": "🧸",
    "telephone": "☎",
    "telephone_receiver": "📞",
    "telescope": "🔭",
    "television": "📺",
    "ten-thirty": "🕥",
    "ten_o’clock": "🕙",
    "tennis": "🎾",
    "tent": "⛺",
    "test_tube": "🧪",
    "thermometer": "🌡",
    "thinking_face": "🤔",
    "thought_balloon": "💭",
    "thread": "🧵",
    "three-thirty": "🕞",
    "three_o’clock": "🕒",
    "thumbs_down": "👎",
    "thumbs_down_dark_skin_tone": "👎🏿",
    "thumbs_down_light_skin_tone": "👎🏻",
    "thumbs_down_medium-dark_skin_tone": "👎🏾",
    "thumbs_down_medium-light_skin_tone": "👎🏼",
    "thumbs_down_medium_skin_tone": "👎🏽",
    "thumbs_up": "👍",
    "thumbs_up_dark_skin_tone": "👍🏿",
    "thumbs_up_light_skin_tone": "👍🏻",
    "thumbs_up_medium-dark_skin_tone": "👍🏾",
    "thumbs_up_medium-light_skin_tone": "👍🏼",
    "thumbs_up_medium_skin_tone": "👍🏽",
    "ticket": "🎫",
    "tiger": "🐯",
    "tiger_face": "🐯",
    "timer_clock": "⏲",
    "tired_face": "😫",
    "toolbox": "🧰",
    "toilet": "🚽",
    "tomato": "🍅",
    "tongue": "👅",
    "tooth": "🦷",
    "top_hat": "🎩",
    "tornado": "🌪",
    "trackball": "🖲",
    "tractor": "🚜",
    "trade_mark": "™",
    "train": "🚋",
    "tram": "🚊",
    "tram_car": "🚋",
    "triangular_flag": "🚩",
    "triangular_ruler": "📐",
    "trident_emblem": "🔱",
    "trolleybus": "🚎",
    "trophy": "🏆",
    "tropical_drink": "🍹",
    "tropical_fish": "🐠",
    "trumpet": "🎺",
    "tulip": "🌷",
    "tumbler_glass": "🥃",
    "turtle": "🐢",
    "twelve-thirty": "🕧",
    "twelve_o’clock": "🕛",
    "two-hump_camel": "🐫",
    "two-thirty": "🕝",
    "two_hearts": "💕",
    "two_men_holding_hands": "👬",
    "two_o’clock": "🕑",
    "two_women_holding_hands": "👭",
    "umbrella": "☂",
    "umbrella_on_ground": "⛱",
    "umbrella_with_rain_drops": "☔",
    "unamused_face": "😒",
    "unicorn_face": "🦄",
    "unlocked": "🔓",
    "up-down_arrow": "↕",
    "up-left_arrow": "↖",
    "up-right_arrow": "↗",
    "up_arrow": "⬆",
    "upside-down_face": "🙃",
    "upwards_button": "🔼",
    "vampire": "🧛",
    "vampire_dark_skin_tone": "🧛🏿",
    "vampire_light_skin_tone": "🧛🏻",
    "vampire_medium-dark_skin_tone": "🧛🏾",
    "vampire_medium-light_skin_tone": "🧛🏼",
    "vampire_medium_skin_tone": "🧛🏽",
    "vertical_traffic_light": "🚦",
    "vibration_mode": "📳",
    "victory_hand": "✌",
    "victory_hand_dark_skin_tone": "✌🏿",
    "victory_hand_light_skin_tone": "✌🏻",
    "victory_hand_medium-dark_skin_tone": "✌🏾",
    "victory_hand_medium-light_skin_tone": "✌🏼",
    "victory_hand_medium_skin_tone": "✌🏽",
    "video_camera": "📹",
    "video_game": "🎮",
    "videocassette": "📼",
    "violin": "🎻",
    "volcano": "🌋",
    "volleyball": "🏐",
    "vulcan_salute": "🖖",
    "vulcan_salute_dark_skin_tone": "🖖🏿",
    "vulcan_salute_light_skin_tone": "🖖🏻",
    "vulcan_salute_medium-dark_skin_tone": "🖖🏾",
    "vulcan_salute_medium-light_skin_tone": "🖖🏼",
    "vulcan_salute_medium_skin_tone": "🖖🏽",
    "waffle": "🧇",
    "waning_crescent_moon": "🌘",
    "waning_gibbous_moon": "🌖",
    "warning": "⚠",
    "wastebasket": "🗑",
    "watch": "⌚",
    "water_buffalo": "🐃",
    "water_closet": "🚾",
    "water_wave": "🌊",
    "watermelon": "🍉",
    "waving_hand": "👋",
    "waving_hand_dark_skin_tone": "👋🏿",
    "waving_hand_light_skin_tone": "👋🏻",
    "waving_hand_medium-dark_skin_tone": "👋🏾",
    "waving_hand_medium-light_skin_tone": "👋🏼",
    "waving_hand_medium_skin_tone": "👋🏽",
    "wavy_dash": "〰",
    "waxing_crescent_moon": "🌒",
    "waxing_gibbous_moon": "🌔",
    "weary_cat_face": "🙀",
    "weary_face": "😩",
    "wedding": "💒",
    "whale": "🐳",
    "wheel_of_dharma": "☸",
    "wheelchair_symbol": "♿",
    "white_circle": "⚪",
    "white_exclamation_mark": "❕",
    "white_flag": "🏳",
    "white_flower": "💮",
    "white_hair": "🦳",
    "white-haired_man": "👨\u200d🦳",
    "white-haired_woman": "👩\u200d🦳",
    "white_heart": "🤍",
    "white_heavy_check_mark": "✅",
    "white_large_square": "⬜",
    "white_medium-small_square": "◽",
    "white_medium_square": "◻",
    "white_medium_star": "⭐",
    "white_question_mark": "❔",
    "white_small_square": "▫",
    "white_square_button": "🔳",
    "wilted_flower": "🥀",
    "wind_chime": "🎐",
    "wind_face": "🌬",
    "wine_glass": "🍷",
    "winking_face": "😉",
    "winking_face_with_tongue": "😜",
    "wolf_face": "🐺",
    "woman": "👩",
    "woman_artist": "👩\u200d🎨",
    "woman_artist_dark_skin_tone": "👩🏿\u200d🎨",
    "woman_artist_light_skin_tone": "👩🏻\u200d🎨",
    "woman_artist_medium-dark_skin_tone": "👩🏾\u200d🎨",
    "woman_artist_medium-light_skin_tone": "👩🏼\u200d🎨",
    "woman_artist_medium_skin_tone": "👩🏽\u200d🎨",
    "woman_astronaut": "👩\u200d🚀",
    "woman_astronaut_dark_skin_tone": "👩🏿\u200d🚀",
    "woman_astronaut_light_skin_tone": "👩🏻\u200d🚀",
    "woman_astronaut_medium-dark_skin_tone": "👩🏾\u200d🚀",
    "woman_astronaut_medium-light_skin_tone": "👩🏼\u200d🚀",
    "woman_astronaut_medium_skin_tone": "👩🏽\u200d🚀",
    "woman_biking": "🚴\u200d♀️",
    "woman_biking_dark_skin_tone": "🚴🏿\u200d♀️",
    "woman_biking_light_skin_tone": "🚴🏻\u200d♀️",
    "woman_biking_medium-dark_skin_tone": "🚴🏾\u200d♀️",
    "woman_biking_medium-light_skin_tone": "🚴🏼\u200d♀️",
    "woman_biking_medium_skin_tone": "🚴🏽\u200d♀️",
    "woman_bouncing_ball": "⛹️\u200d♀️",
    "woman_bouncing_ball_dark_skin_tone": "⛹🏿\u200d♀️",
    "woman_bouncing_ball_light_skin_tone": "⛹🏻\u200d♀️",
    "woman_bouncing_ball_medium-dark_skin_tone": "⛹🏾\u200d♀️",
    "woman_bouncing_ball_medium-light_skin_tone": "⛹🏼\u200d♀️",
    "woman_bouncing_ball_medium_skin_tone": "⛹🏽\u200d♀️",
    "woman_bowing": "🙇\u200d♀️",
    "woman_bowing_dark_skin_tone": "🙇🏿\u200d♀️",
    "woman_bowing_light_skin_tone": "🙇🏻\u200d♀️",
    "woman_bowing_medium-dark_skin_tone": "🙇🏾\u200d♀️",
    "woman_bowing_medium-light_skin_tone": "🙇🏼\u200d♀️",
    "woman_bowing_medium_skin_tone": "🙇🏽\u200d♀️",
    "woman_cartwheeling": "🤸\u200d♀️",
    "woman_cartwheeling_dark_skin_tone": "🤸🏿\u200d♀️",
    "woman_cartwheeling_light_skin_tone": "🤸🏻\u200d♀️",
    "woman_cartwheeling_medium-dark_skin_tone": "🤸🏾\u200d♀️",
    "woman_cartwheeling_medium-light_skin_tone": "🤸🏼\u200d♀️",
    "woman_cartwheeling_medium_skin_tone": "🤸🏽\u200d♀️",
    "woman_climbing": "🧗\u200d♀️",
    "woman_climbing_dark_skin_tone": "🧗🏿\u200d♀️",
    "woman_climbing_light_skin_tone": "🧗🏻\u200d♀️",
    "woman_climbing_medium-dark_skin_tone": "🧗🏾\u200d♀️",
    "woman_climbing_medium-light_skin_tone": "🧗🏼\u200d♀️",
    "woman_climbing_medium_skin_tone": "🧗🏽\u200d♀️",
    "woman_construction_worker": "👷\u200d♀️",
    "woman_construction_worker_dark_skin_tone": "👷🏿\u200d♀️",
    "woman_construction_worker_light_skin_tone": "👷🏻\u200d♀️",
    "woman_construction_worker_medium-dark_skin_tone": "👷🏾\u200d♀️",
    "woman_construction_worker_medium-light_skin_tone": "👷🏼\u200d♀️",
    "woman_construction_worker_medium_skin_tone": "👷🏽\u200d♀️",
    "woman_cook": "👩\u200d🍳",
    "woman_cook_dark_skin_tone": "👩🏿\u200d🍳",
    "woman_cook_light_skin_tone": "👩🏻\u200d🍳",
    "woman_cook_medium-dark_skin_tone": "👩🏾\u200d🍳",
    "woman_cook_medium-light_skin_tone": "👩🏼\u200d🍳",
    "woman_cook_medium_skin_tone": "👩🏽\u200d🍳",
    "woman_dancing": "💃",
    "woman_dancing_dark_skin_tone": "💃🏿",
    "woman_dancing_light_skin_tone": "💃🏻",
    "woman_dancing_medium-dark_skin_tone": "💃🏾",
    "woman_dancing_medium-light_skin_tone": "💃🏼",
    "woman_dancing_medium_skin_tone": "💃🏽",
    "woman_dark_skin_tone": "👩🏿",
    "woman_detective": "🕵️\u200d♀️",
    "woman_detective_dark_skin_tone": "🕵🏿\u200d♀️",
    "woman_detective_light_skin_tone": "🕵🏻\u200d♀️",
    "woman_detective_medium-dark_skin_tone": "🕵🏾\u200d♀️",
    "woman_detective_medium-light_skin_tone": "🕵🏼\u200d♀️",
    "woman_detective_medium_skin_tone": "🕵🏽\u200d♀️",
    "woman_elf": "🧝\u200d♀️",
    "woman_elf_dark_skin_tone": "🧝🏿\u200d♀️",
    "woman_elf_light_skin_tone": "🧝🏻\u200d♀️",
    "woman_elf_medium-dark_skin_tone": "🧝🏾\u200d♀️",
    "woman_elf_medium-light_skin_tone": "🧝🏼\u200d♀️",
    "woman_elf_medium_skin_tone": "🧝🏽\u200d♀️",
    "woman_facepalming": "🤦\u200d♀️",
    "woman_facepalming_dark_skin_tone": "🤦🏿\u200d♀️",
    "woman_facepalming_light_skin_tone": "🤦🏻\u200d♀️",
    "woman_facepalming_medium-dark_skin_tone": "🤦🏾\u200d♀️",
    "woman_facepalming_medium-light_skin_tone": "🤦🏼\u200d♀️",
    "woman_facepalming_medium_skin_tone": "🤦🏽\u200d♀️",
    "woman_factory_worker": "👩\u200d🏭",
    "woman_factory_worker_dark_skin_tone": "👩🏿\u200d🏭",
    "woman_factory_worker_light_skin_tone": "👩🏻\u200d🏭",
    "woman_factory_worker_medium-dark_skin_tone": "👩🏾\u200d🏭",
    "woman_factory_worker_medium-light_skin_tone": "👩🏼\u200d🏭",
    "woman_factory_worker_medium_skin_tone": "👩🏽\u200d🏭",
    "woman_fairy": "🧚\u200d♀️",
    "woman_fairy_dark_skin_tone": "🧚🏿\u200d♀️",
    "woman_fairy_light_skin_tone": "🧚🏻\u200d♀️",
    "woman_fairy_medium-dark_skin_tone": "🧚🏾\u200d♀️",
    "woman_fairy_medium-light_skin_tone": "🧚🏼\u200d♀️",
    "woman_fairy_medium_skin_tone": "🧚🏽\u200d♀️",
    "woman_farmer": "👩\u200d🌾",
    "woman_farmer_dark_skin_tone": "👩🏿\u200d🌾",
    "woman_farmer_light_skin_tone": "👩🏻\u200d🌾",
    "woman_farmer_medium-dark_skin_tone": "👩🏾\u200d🌾",
    "woman_farmer_medium-light_skin_tone": "👩🏼\u200d🌾",
    "woman_farmer_medium_skin_tone": "👩🏽\u200d🌾",
    "woman_firefighter": "👩\u200d🚒",
    "woman_firefighter_dark_skin_tone": "👩🏿\u200d🚒",
    "woman_firefighter_light_skin_tone": "👩🏻\u200d🚒",
    "woman_firefighter_medium-dark_skin_tone": "👩🏾\u200d🚒",
    "woman_firefighter_medium-light_skin_tone": "👩🏼\u200d🚒",
    "woman_firefighter_medium_skin_tone": "👩🏽\u200d🚒",
    "woman_frowning": "🙍\u200d♀️",
    "woman_frowning_dark_skin_tone": "🙍🏿\u200d♀️",
    "woman_frowning_light_skin_tone": "🙍🏻\u200d♀️",
    "woman_frowning_medium-dark_skin_tone": "🙍🏾\u200d♀️",
    "woman_frowning_medium-light_skin_tone": "🙍🏼\u200d♀️",
    "woman_frowning_medium_skin_tone": "🙍🏽\u200d♀️",
    "woman_genie": "🧞\u200d♀️",
    "woman_gesturing_no": "🙅\u200d♀️",
    "woman_gesturing_no_dark_skin_tone": "🙅🏿\u200d♀️",
    "woman_gesturing_no_light_skin_tone": "🙅🏻\u200d♀️",
    "woman_gesturing_no_medium-dark_skin_tone": "🙅🏾\u200d♀️",
    "woman_gesturing_no_medium-light_skin_tone": "🙅🏼\u200d♀️",
    "woman_gesturing_no_medium_skin_tone": "🙅🏽\u200d♀️",
    "woman_gesturing_ok": "🙆\u200d♀️",
    "woman_gesturing_ok_dark_skin_tone": "🙆🏿\u200d♀️",
    "woman_gesturing_ok_light_skin_tone": "🙆🏻\u200d♀️",
    "woman_gesturing_ok_medium-dark_skin_tone": "🙆🏾\u200d♀️",
    "woman_gesturing_ok_medium-light_skin_tone": "🙆🏼\u200d♀️",
    "woman_gesturing_ok_medium_skin_tone": "🙆🏽\u200d♀️",
    "woman_getting_haircut": "💇\u200d♀️",
    "woman_getting_haircut_dark_skin_tone": "💇🏿\u200d♀️",
    "woman_getting_haircut_light_skin_tone": "💇🏻\u200d♀️",
    "woman_getting_haircut_medium-dark_skin_tone": "💇🏾\u200d♀️",
    "woman_getting_haircut_medium-light_skin_tone": "💇🏼\u200d♀️",
    "woman_getting_haircut_medium_skin_tone": "💇🏽\u200d♀️",
    "woman_getting_massage": "💆\u200d♀️",
    "woman_getting_massage_dark_skin_tone": "💆🏿\u200d♀️",
    "woman_getting_massage_light_skin_tone": "💆🏻\u200d♀️",
    "woman_getting_massage_medium-dark_skin_tone": "💆🏾\u200d♀️",
    "woman_getting_massage_medium-light_skin_tone": "💆🏼\u200d♀️",
    "woman_getting_massage_medium_skin_tone": "💆🏽\u200d♀️",
    "woman_golfing": "🏌️\u200d♀️",
    "woman_golfing_dark_skin_tone": "🏌🏿\u200d♀️",
    "woman_golfing_light_skin_tone": "🏌🏻\u200d♀️",
    "woman_golfing_medium-dark_skin_tone": "🏌🏾\u200d♀️",
    "woman_golfing_medium-light_skin_tone": "🏌🏼\u200d♀️",
    "woman_golfing_medium_skin_tone": "🏌🏽\u200d♀️",
    "woman_guard": "💂\u200d♀️",
    "woman_guard_dark_skin_tone": "💂🏿\u200d♀️",
    "woman_guard_light_skin_tone": "💂🏻\u200d♀️",
    "woman_guard_medium-dark_skin_tone": "💂🏾\u200d♀️",
    "woman_guard_medium-light_skin_tone": "💂🏼\u200d♀️",
    "woman_guard_medium_skin_tone": "💂🏽\u200d♀️",
    "woman_health_worker": "👩\u200d⚕️",
    "woman_health_worker_dark_skin_tone": "👩🏿\u200d⚕️",
    "woman_health_worker_light_skin_tone": "👩🏻\u200d⚕️",
    "woman_health_worker_medium-dark_skin_tone": "👩🏾\u200d⚕️",
    "woman_health_worker_medium-light_skin_tone": "👩🏼\u200d⚕️",
    "woman_health_worker_medium_skin_tone": "👩🏽\u200d⚕️",
    "woman_in_lotus_position": "🧘\u200d♀️",
    "woman_in_lotus_position_dark_skin_tone": "🧘🏿\u200d♀️",
    "woman_in_lotus_position_light_skin_tone": "🧘🏻\u200d♀️",
    "woman_in_lotus_position_medium-dark_skin_tone": "🧘🏾\u200d♀️",
    "woman_in_lotus_position_medium-light_skin_tone": "🧘🏼\u200d♀️",
    "woman_in_lotus_position_medium_skin_tone": "🧘🏽\u200d♀️",
    "woman_in_manual_wheelchair": "👩\u200d🦽",
    "woman_in_motorized_wheelchair": "👩\u200d🦼",
    "woman_in_steamy_room": "🧖\u200d♀️",
    "woman_in_steamy_room_dark_skin_tone": "🧖🏿\u200d♀️",
    "woman_in_steamy_room_light_skin_tone": "🧖🏻\u200d♀️",
    "woman_in_steamy_room_medium-dark_skin_tone": "🧖🏾\u200d♀️",
    "woman_in_steamy_room_medium-light_skin_tone": "🧖🏼\u200d♀️",
    "woman_in_steamy_room_medium_skin_tone": "🧖🏽\u200d♀️",
    "woman_judge": "👩\u200d⚖️",
    "woman_judge_dark_skin_tone": "👩🏿\u200d⚖️",
    "woman_judge_light_skin_tone": "👩🏻\u200d⚖️",
    "woman_judge_medium-dark_skin_tone": "👩🏾\u200d⚖️",
    "woman_judge_medium-light_skin_tone": "👩🏼\u200d⚖️",
    "woman_judge_medium_skin_tone": "👩🏽\u200d⚖️",
    "woman_juggling": "🤹\u200d♀️",
    "woman_juggling_dark_skin_tone": "🤹🏿\u200d♀️",
    "woman_juggling_light_skin_tone": "🤹🏻\u200d♀️",
    "woman_juggling_medium-dark_skin_tone": "🤹🏾\u200d♀️",
    "woman_juggling_medium-light_skin_tone": "🤹🏼\u200d♀️",
    "woman_juggling_medium_skin_tone": "🤹🏽\u200d♀️",
    "woman_lifting_weights": "🏋️\u200d♀️",
    "woman_lifting_weights_dark_skin_tone": "🏋🏿\u200d♀️",
    "woman_lifting_weights_light_skin_tone": "🏋🏻\u200d♀️",
    "woman_lifting_weights_medium-dark_skin_tone": "🏋🏾\u200d♀️",
    "woman_lifting_weights_medium-light_skin_tone": "🏋🏼\u200d♀️",
    "woman_lifting_weights_medium_skin_tone": "🏋🏽\u200d♀️",
    "woman_light_skin_tone": "👩🏻",
    "woman_mage": "🧙\u200d♀️",
    "woman_mage_dark_skin_tone": "🧙🏿\u200d♀️",
    "woman_mage_light_skin_tone": "🧙🏻\u200d♀️",
    "woman_mage_medium-dark_skin_tone": "🧙🏾\u200d♀️",
    "woman_mage_medium-light_skin_tone": "🧙🏼\u200d♀️",
    "woman_mage_medium_skin_tone": "🧙🏽\u200d♀️",
    "woman_mechanic": "👩\u200d🔧",
    "woman_mechanic_dark_skin_tone": "👩🏿\u200d🔧",
    "woman_mechanic_light_skin_tone": "👩🏻\u200d🔧",
    "woman_mechanic_medium-dark_skin_tone": "👩🏾\u200d🔧",
    "woman_mechanic_medium-light_skin_tone": "👩🏼\u200d🔧",
    "woman_mechanic_medium_skin_tone": "👩🏽\u200d🔧",
    "woman_medium-dark_skin_tone": "👩🏾",
    "woman_medium-light_skin_tone": "👩🏼",
    "woman_medium_skin_tone": "👩🏽",
    "woman_mountain_biking": "🚵\u200d♀️",
    "woman_mountain_biking_dark_skin_tone": "🚵🏿\u200d♀️",
    "woman_mountain_biking_light_skin_tone": "🚵🏻\u200d♀️",
    "woman_mountain_biking_medium-dark_skin_tone": "🚵🏾\u200d♀️",
    "woman_mountain_biking_medium-light_skin_tone": "🚵🏼\u200d♀️",
    "woman_mountain_biking_medium_skin_tone": "🚵🏽\u200d♀️",
    "woman_office_worker": "👩\u200d💼",
    "woman_office_worker_dark_skin_tone": "👩🏿\u200d💼",
    "woman_office_worker_light_skin_tone": "👩🏻\u200d💼",
    "woman_office_worker_medium-dark_skin_tone": "👩🏾\u200d💼",
    "woman_office_worker_medium-light_skin_tone": "👩🏼\u200d💼",
    "woman_office_worker_medium_skin_tone": "👩🏽\u200d💼",
    "woman_pilot": "👩\u200d✈️",
    "woman_pilot_dark_skin_tone": "👩🏿\u200d✈️",
    "woman_pilot_light_skin_tone": "👩🏻\u200d✈️",
    "woman_pilot_medium-dark_skin_tone": "👩🏾\u200d✈️",
    "woman_pilot_medium-light_skin_tone": "👩🏼\u200d✈️",
    "woman_pilot_medium_skin_tone": "👩🏽\u200d✈️",
    "woman_playing_handball": "🤾\u200d♀️",
    "woman_playing_handball_dark_skin_tone": "🤾🏿\u200d♀️",
    "woman_playing_handball_light_skin_tone": "🤾🏻\u200d♀️",
    "woman_playing_handball_medium-dark_skin_tone": "🤾🏾\u200d♀️",
    "woman_playing_handball_medium-light_skin_tone": "🤾🏼\u200d♀️",
    "woman_playing_handball_medium_skin_tone": "🤾🏽\u200d♀️",
    "woman_playing_water_polo": "🤽\u200d♀️",
    "woman_playing_water_polo_dark_skin_tone": "🤽🏿\u200d♀️",
    "woman_playing_water_polo_light_skin_tone": "🤽🏻\u200d♀️",
    "woman_playing_water_polo_medium-dark_skin_tone": "🤽🏾\u200d♀️",
    "woman_playing_water_polo_medium-light_skin_tone": "🤽🏼\u200d♀️",
    "woman_playing_water_polo_medium_skin_tone": "🤽🏽\u200d♀️",
    "woman_police_officer": "👮\u200d♀️",
    "woman_police_officer_dark_skin_tone": "👮🏿\u200d♀️",
    "woman_police_officer_light_skin_tone": "👮🏻\u200d♀️",
    "woman_police_officer_medium-dark_skin_tone": "👮🏾\u200d♀️",
    "woman_police_officer_medium-light_skin_tone": "👮🏼\u200d♀️",
    "woman_police_officer_medium_skin_tone": "👮🏽\u200d♀️",
    "woman_pouting": "🙎\u200d♀️",
    "woman_pouting_dark_skin_tone": "🙎🏿\u200d♀️",
    "woman_pouting_light_skin_tone": "🙎🏻\u200d♀️",
    "woman_pouting_medium-dark_skin_tone": "🙎🏾\u200d♀️",
    "woman_pouting_medium-light_skin_tone": "🙎🏼\u200d♀️",
    "woman_pouting_medium_skin_tone": "🙎🏽\u200d♀️",
    "woman_raising_hand": "🙋\u200d♀️",
    "woman_raising_hand_dark_skin_tone": "🙋🏿\u200d♀️",
    "woman_raising_hand_light_skin_tone": "🙋🏻\u200d♀️",
    "woman_raising_hand_medium-dark_skin_tone": "🙋🏾\u200d♀️",
    "woman_raising_hand_medium-light_skin_tone": "🙋🏼\u200d♀️",
    "woman_raising_hand_medium_skin_tone": "🙋🏽\u200d♀️",
    "woman_rowing_boat": "🚣\u200d♀️",
    "woman_rowing_boat_dark_skin_tone": "🚣🏿\u200d♀️",
    "woman_rowing_boat_light_skin_tone": "🚣🏻\u200d♀️",
    "woman_rowing_boat_medium-dark_skin_tone": "🚣🏾\u200d♀️",
    "woman_rowing_boat_medium-light_skin_tone": "🚣🏼\u200d♀️",
    "woman_rowing_boat_medium_skin_tone": "🚣🏽\u200d♀️",
    "woman_running": "🏃\u200d♀️",
    "woman_running_dark_skin_tone": "🏃🏿\u200d♀️",
    "woman_running_light_skin_tone": "🏃🏻\u200d♀️",
    "woman_running_medium-dark_skin_tone": "🏃🏾\u200d♀️",
    "woman_running_medium-light_skin_tone": "🏃🏼\u200d♀️",
    "woman_running_medium_skin_tone": "🏃🏽\u200d♀️",
    "woman_scientist": "👩\u200d🔬",
    "woman_scientist_dark_skin_tone": "👩🏿\u200d🔬",
    "woman_scientist_light_skin_tone": "👩🏻\u200d🔬",
    "woman_scientist_medium-dark_skin_tone": "👩🏾\u200d🔬",
    "woman_scientist_medium-light_skin_tone": "👩🏼\u200d🔬",
    "woman_scientist_medium_skin_tone": "👩🏽\u200d🔬",
    "woman_shrugging": "🤷\u200d♀️",
    "woman_shrugging_dark_skin_tone": "🤷🏿\u200d♀️",
    "woman_shrugging_light_skin_tone": "🤷🏻\u200d♀️",
    "woman_shrugging_medium-dark_skin_tone": "🤷🏾\u200d♀️",
    "woman_shrugging_medium-light_skin_tone": "🤷🏼\u200d♀️",
    "woman_shrugging_medium_skin_tone": "🤷🏽\u200d♀️",
    "woman_singer": "👩\u200d🎤",
    "woman_singer_dark_skin_tone": "👩🏿\u200d🎤",
    "woman_singer_light_skin_tone": "👩🏻\u200d🎤",
    "woman_singer_medium-dark_skin_tone": "👩🏾\u200d🎤",
    "woman_singer_medium-light_skin_tone": "👩🏼\u200d🎤",
    "woman_singer_medium_skin_tone": "👩🏽\u200d🎤",
    "woman_student": "👩\u200d🎓",
    "woman_student_dark_skin_tone": "👩🏿\u200d🎓",
    "woman_student_light_skin_tone": "👩🏻\u200d🎓",
    "woman_student_medium-dark_skin_tone": "👩🏾\u200d🎓",
    "woman_student_medium-light_skin_tone": "👩🏼\u200d🎓",
    "woman_student_medium_skin_tone": "👩🏽\u200d🎓",
    "woman_surfing": "🏄\u200d♀️",
    "woman_surfing_dark_skin_tone": "🏄🏿\u200d♀️",
    "woman_surfing_light_skin_tone": "🏄🏻\u200d♀️",
    "woman_surfing_medium-dark_skin_tone": "🏄🏾\u200d♀️",
    "woman_surfing_medium-light_skin_tone": "🏄🏼\u200d♀️",
    "woman_surfing_medium_skin_tone": "🏄🏽\u200d♀️",
    "woman_swimming": "🏊\u200d♀️",
    "woman_swimming_dark_skin_tone": "🏊🏿\u200d♀️",
    "woman_swimming_light_skin_tone": "🏊🏻\u200d♀️",
    "woman_swimming_medium-dark_skin_tone": "🏊🏾\u200d♀️",
    "woman_swimming_medium-light_skin_tone": "🏊🏼\u200d♀️",
    "woman_swimming_medium_skin_tone": "🏊🏽\u200d♀️",
    "woman_teacher": "👩\u200d🏫",
    "woman_teacher_dark_skin_tone": "👩🏿\u200d🏫",
    "woman_teacher_light_skin_tone": "👩🏻\u200d🏫",
    "woman_teacher_medium-dark_skin_tone": "👩🏾\u200d🏫",
    "woman_teacher_medium-light_skin_tone": "👩🏼\u200d🏫",
    "woman_teacher_medium_skin_tone": "👩🏽\u200d🏫",
    "woman_technologist": "👩\u200d💻",
    "woman_technologist_dark_skin_tone": "👩🏿\u200d💻",
    "woman_technologist_light_skin_tone": "👩🏻\u200d💻",
    "woman_technologist_medium-dark_skin_tone": "👩🏾\u200d💻",
    "woman_technologist_medium-light_skin_tone": "👩🏼\u200d💻",
    "woman_technologist_medium_skin_tone": "👩🏽\u200d💻",
    "woman_tipping_hand": "💁\u200d♀️",
    "woman_tipping_hand_dark_skin_tone": "💁🏿\u200d♀️",
    "woman_tipping_hand_light_skin_tone": "💁🏻\u200d♀️",
    "woman_tipping_hand_medium-dark_skin_tone": "💁🏾\u200d♀️",
    "woman_tipping_hand_medium-light_skin_tone": "💁🏼\u200d♀️",
    "woman_tipping_hand_medium_skin_tone": "💁🏽\u200d♀️",
    "woman_vampire": "🧛\u200d♀️",
    "woman_vampire_dark_skin_tone": "🧛🏿\u200d♀️",
    "woman_vampire_light_skin_tone": "🧛🏻\u200d♀️",
    "woman_vampire_medium-dark_skin_tone": "🧛🏾\u200d♀️",
    "woman_vampire_medium-light_skin_tone": "🧛🏼\u200d♀️",
    "woman_vampire_medium_skin_tone": "🧛🏽\u200d♀️",
    "woman_walking": "🚶\u200d♀️",
    "woman_walking_dark_skin_tone": "🚶🏿\u200d♀️",
    "woman_walking_light_skin_tone": "🚶🏻\u200d♀️",
    "woman_walking_medium-dark_skin_tone": "🚶🏾\u200d♀️",
    "woman_walking_medium-light_skin_tone": "🚶🏼\u200d♀️",
    "woman_walking_medium_skin_tone": "🚶🏽\u200d♀️",
    "woman_wearing_turban": "👳\u200d♀️",
    "woman_wearing_turban_dark_skin_tone": "👳🏿\u200d♀️",
    "woman_wearing_turban_light_skin_tone": "👳🏻\u200d♀️",
    "woman_wearing_turban_medium-dark_skin_tone": "👳🏾\u200d♀️",
    "woman_wearing_turban_medium-light_skin_tone": "👳🏼\u200d♀️",
    "woman_wearing_turban_medium_skin_tone": "👳🏽\u200d♀️",
    "woman_with_headscarf": "🧕",
    "woman_with_headscarf_dark_skin_tone": "🧕🏿",
    "woman_with_headscarf_light_skin_tone": "🧕🏻",
    "woman_with_headscarf_medium-dark_skin_tone": "🧕🏾",
    "woman_with_headscarf_medium-light_skin_tone": "🧕🏼",
    "woman_with_headscarf_medium_skin_tone": "🧕🏽",
    "woman_with_probing_cane": "👩\u200d🦯",
    "woman_zombie": "🧟\u200d♀️",
    "woman’s_boot": "👢",
    "woman’s_clothes": "👚",
    "woman’s_hat": "👒",
    "woman’s_sandal": "👡",
    "women_with_bunny_ears": "👯\u200d♀️",
    "women_wrestling": "🤼\u200d♀️",
    "women’s_room": "🚺",
    "woozy_face": "🥴",
    "world_map": "🗺",
    "worried_face": "😟",
    "wrapped_gift": "🎁",
    "wrench": "🔧",
    "writing_hand": "✍",
    "writing_hand_dark_skin_tone": "✍🏿",
    "writing_hand_light_skin_tone": "✍🏻",
    "writing_hand_medium-dark_skin_tone": "✍🏾",
    "writing_hand_medium-light_skin_tone": "✍🏼",
    "writing_hand_medium_skin_tone": "✍🏽",
    "yarn": "🧶",
    "yawning_face": "🥱",
    "yellow_circle": "🟡",
    "yellow_heart": "💛",
    "yellow_square": "🟨",
    "yen_banknote": "💴",
    "yo-yo": "🪀",
    "yin_yang": "☯",
    "zany_face": "🤪",
    "zebra": "🦓",
    "zipper-mouth_face": "🤐",
    "zombie": "🧟",
    "zzz": "💤",
    "åland_islands": "🇦🇽",
    "keycap_asterisk": "*⃣",
    "keycap_digit_eight": "8⃣",
    "keycap_digit_five": "5⃣",
    "keycap_digit_four": "4⃣",
    "keycap_digit_nine": "9⃣",
    "keycap_digit_one": "1⃣",
    "keycap_digit_seven": "7⃣",
    "keycap_digit_six": "6⃣",
    "keycap_digit_three": "3⃣",
    "keycap_digit_two": "2⃣",
    "keycap_digit_zero": "0⃣",
    "keycap_number_sign": "#⃣",
    "light_skin_tone": "🏻",
    "medium_light_skin_tone": "🏼",
    "medium_skin_tone": "🏽",
    "medium_dark_skin_tone": "🏾",
    "dark_skin_tone": "🏿",
    "regional_indicator_symbol_letter_a": "🇦",
    "regional_indicator_symbol_letter_b": "🇧",
    "regional_indicator_symbol_letter_c": "🇨",
    "regional_indicator_symbol_letter_d": "🇩",
    "regional_indicator_symbol_letter_e": "🇪",
    "regional_indicator_symbol_letter_f": "🇫",
    "regional_indicator_symbol_letter_g": "🇬",
    "regional_indicator_symbol_letter_h": "🇭",
    "regional_indicator_symbol_letter_i": "🇮",
    "regional_indicator_symbol_letter_j": "🇯",
    "regional_indicator_symbol_letter_k": "🇰",
    "regional_indicator_symbol_letter_l": "🇱",
    "regional_indicator_symbol_letter_m": "🇲",
    "regional_indicator_symbol_letter_n": "🇳",
    "regional_indicator_symbol_letter_o": "🇴",
    "regional_indicator_symbol_letter_p": "🇵",
    "regional_indicator_symbol_letter_q": "🇶",
    "regional_indicator_symbol_letter_r": "🇷",
    "regional_indicator_symbol_letter_s": "🇸",
    "regional_indicator_symbol_letter_t": "🇹",
    "regional_indicator_symbol_letter_u": "🇺",
    "regional_indicator_symbol_letter_v": "🇻",
    "regional_indicator_symbol_letter_w": "🇼",
    "regional_indicator_symbol_letter_x": "🇽",
    "regional_indicator_symbol_letter_y": "🇾",
    "regional_indicator_symbol_letter_z": "🇿",
    "airplane_arriving": "🛬",
    "space_invader": "👾",
    "football": "🏈",
    "anger": "💢",
    "angry": "😠",
    "anguished": "😧",
    "signal_strength": "📶",
    "arrows_counterclockwise": "🔄",
    "arrow_heading_down": "⤵",
    "arrow_heading_up": "⤴",
    "art": "🎨",
    "astonished": "😲",
    "athletic_shoe": "👟",
    "atm": "🏧",
    "car": "🚗",
    "red_car": "🚗",
    "angel": "👼",
    "back": "🔙",
    "badminton_racquet_and_shuttlecock": "🏸",
    "dollar": "💵",
    "euro": "💶",
    "pound": "💷",
    "yen": "💴",
    "barber": "💈",
    "bath": "🛀",
    "bear": "🐻",
    "heartbeat": "💓",
    "beer": "🍺",
    "no_bell": "🔕",
    "bento": "🍱",
    "bike": "🚲",
    "bicyclist": "🚴",
    "8ball": "🎱",
    "biohazard_sign": "☣",
    "birthday": "🎂",
    "black_circle_for_record": "⏺",
    "clubs": "♣",
    "diamonds": "♦",
    "arrow_double_down": "⏬",
    "hearts": "♥",
    "rewind": "⏪",
    "black_left__pointing_double_triangle_with_vertical_bar": "⏮",
    "arrow_backward": "◀",
    "black_medium_small_square": "◾",
    "question": "❓",
    "fast_forward": "⏩",
    "black_right__pointing_double_triangle_with_vertical_bar": "⏭",
    "arrow_forward": "▶",
    "black_right__pointing_triangle_with_double_vertical_bar": "⏯",
    "arrow_right": "➡",
    "spades": "♠",
    "black_square_for_stop": "⏹",
    "sunny": "☀",
    "phone": "☎",
    "recycle": "♻",
    "arrow_double_up": "⏫",
    "busstop": "🚏",
    "date": "📅",
    "flags": "🎏",
    "cat2": "🐈",
    "joy_cat": "😹",
    "smirk_cat": "😼",
    "chart_with_downwards_trend": "📉",
    "chart_with_upwards_trend": "📈",
    "chart": "💹",
    "mega": "📣",
    "checkered_flag": "🏁",
    "accept": "🉑",
    "ideograph_advantage": "🉐",
    "congratulations": "㊗",
    "secret": "㊙",
    "m": "Ⓜ",
    "city_sunset": "🌆",
    "clapper": "🎬",
    "clap": "👏",
    "beers": "🍻",
    "clock830": "🕣",
    "clock8": "🕗",
    "clock1130": "🕦",
    "clock11": "🕚",
    "clock530": "🕠",
    "clock5": "🕔",
    "clock430": "🕟",
    "clock4": "🕓",
    "clock930": "🕤",
    "clock9": "🕘",
    "clock130": "🕜",
    "clock1": "🕐",
    "clock730": "🕢",
    "clock7": "🕖",
    "clock630": "🕡",
    "clock6": "🕕",
    "clock1030": "🕥",
    "clock10": "🕙",
    "clock330": "🕞",
    "clock3": "🕒",
    "clock1230": "🕧",
    "clock12": "🕛",
    "clock230": "🕝",
    "clock2": "🕑",
    "arrows_clockwise": "🔃",
    "repeat": "🔁",
    "repeat_one": "🔂",
    "closed_lock_with_key": "🔐",
    "mailbox_closed": "📪",
    "mailbox": "📫",
    "cloud_with_tornado": "🌪",
    "cocktail": "🍸",
    "boom": "💥",
    "compression": "🗜",
    "confounded": "😖",
    "confused": "😕",
    "rice": "🍚",
    "cow2": "🐄",
    "cricket_bat_and_ball": "🏏",
    "x": "❌",
    "cry": "😢",
    "curry": "🍛",
    "dagger_knife": "🗡",
    "dancer": "💃",
    "dark_sunglasses": "🕶",
    "dash": "💨",
    "truck": "🚚",
    "derelict_house_building": "🏚",
    "diamond_shape_with_a_dot_inside": "💠",
    "dart": "🎯",
    "disappointed_relieved": "😥",
    "disappointed": "😞",
    "do_not_litter": "🚯",
    "dog2": "🐕",
    "flipper": "🐬",
    "loop": "➿",
    "bangbang": "‼",
    "double_vertical_bar": "⏸",
    "dove_of_peace": "🕊",
    "small_red_triangle_down": "🔻",
    "arrow_down_small": "🔽",
    "arrow_down": "⬇",
    "dromedary_camel": "🐪",
    "e__mail": "📧",
    "corn": "🌽",
    "ear_of_rice": "🌾",
    "earth_americas": "🌎",
    "earth_asia": "🌏",
    "earth_africa": "🌍",
    "eight_pointed_black_star": "✴",
    "eight_spoked_asterisk": "✳",
    "eject_symbol": "⏏",
    "bulb": "💡",
    "emoji_modifier_fitzpatrick_type__1__2": "🏻",
    "emoji_modifier_fitzpatrick_type__3": "🏼",
    "emoji_modifier_fitzpatrick_type__4": "🏽",
    "emoji_modifier_fitzpatrick_type__5": "🏾",
    "emoji_modifier_fitzpatrick_type__6": "🏿",
    "end": "🔚",
    "email": "✉",
    "european_castle": "🏰",
    "european_post_office": "🏤",
    "interrobang": "⁉",
    "expressionless": "😑",
    "eyeglasses": "👓",
    "massage": "💆",
    "yum": "😋",
    "scream": "😱",
    "kissing_heart": "😘",
    "sweat": "😓",
    "face_with_head__bandage": "🤕",
    "triumph": "😤",
    "mask": "😷",
    "no_good": "🙅",
    "ok_woman": "🙆",
    "open_mouth": "😮",
    "cold_sweat": "😰",
    "stuck_out_tongue": "😛",
    "stuck_out_tongue_closed_eyes": "😝",
    "stuck_out_tongue_winking_eye": "😜",
    "joy": "😂",
    "no_mouth": "😶",
    "santa": "🎅",
    "fax": "📠",
    "fearful": "😨",
    "field_hockey_stick_and_ball": "🏑",
    "first_quarter_moon_with_face": "🌛",
    "fish_cake": "🍥",
    "fishing_pole_and_fish": "🎣",
    "facepunch": "👊",
    "punch": "👊",
    "flag_for_afghanistan": "🇦🇫",
    "flag_for_albania": "🇦🇱",
    "flag_for_algeria": "🇩🇿",
    "flag_for_american_samoa": "🇦🇸",
    "flag_for_andorra": "🇦🇩",
    "flag_for_angola": "🇦🇴",
    "flag_for_anguilla": "🇦🇮",
    "flag_for_antarctica": "🇦🇶",
    "flag_for_antigua_&_barbuda": "🇦🇬",
    "flag_for_argentina": "🇦🇷",
    "flag_for_armenia": "🇦🇲",
    "flag_for_aruba": "🇦🇼",
    "flag_for_ascension_island": "🇦🇨",
    "flag_for_australia": "🇦🇺",
    "flag_for_austria": "🇦🇹",
    "flag_for_azerbaijan": "🇦🇿",
    "flag_for_bahamas": "🇧🇸",
    "flag_for_bahrain": "🇧🇭",
    "flag_for_bangladesh": "🇧🇩",
    "flag_for_barbados": "🇧🇧",
    "flag_for_belarus": "🇧🇾",
    "flag_for_belgium": "🇧🇪",
    "flag_for_belize": "🇧🇿",
    "flag_for_benin": "🇧🇯",
    "flag_for_bermuda": "🇧🇲",
    "flag_for_bhutan": "🇧🇹",
    "flag_for_bolivia": "🇧🇴",
    "flag_for_bosnia_&_herzegovina": "🇧🇦",
    "flag_for_botswana": "🇧🇼",
    "flag_for_bouvet_island": "🇧🇻",
    "flag_for_brazil": "🇧🇷",
    "flag_for_british_indian_ocean_territory": "🇮🇴",
    "flag_for_british_virgin_islands": "🇻🇬",
    "flag_for_brunei": "🇧🇳",
    "flag_for_bulgaria": "🇧🇬",
    "flag_for_burkina_faso": "🇧🇫",
    "flag_for_burundi": "🇧🇮",
    "flag_for_cambodia": "🇰🇭",
    "flag_for_cameroon": "🇨🇲",
    "flag_for_canada": "🇨🇦",
    "flag_for_canary_islands": "🇮🇨",
    "flag_for_cape_verde": "🇨🇻",
    "flag_for_caribbean_netherlands": "🇧🇶",
    "flag_for_cayman_islands": "🇰🇾",
    "flag_for_central_african_republic": "🇨🇫",
    "flag_for_ceuta_&_melilla": "🇪🇦",
    "flag_for_chad": "🇹🇩",
    "flag_for_chile": "🇨🇱",
    "flag_for_china": "🇨🇳",
    "flag_for_christmas_island": "🇨🇽",
    "flag_for_clipperton_island": "🇨🇵",
    "flag_for_cocos__islands": "🇨🇨",
    "flag_for_colombia": "🇨🇴",
    "flag_for_comoros": "🇰🇲",
    "flag_for_congo____brazzaville": "🇨🇬",
    "flag_for_congo____kinshasa": "🇨🇩",
    "flag_for_cook_islands": "🇨🇰",
    "flag_for_costa_rica": "🇨🇷",
    "flag_for_croatia": "🇭🇷",
    "flag_for_cuba": "🇨🇺",
    "flag_for_curaçao": "🇨🇼",
    "flag_for_cyprus": "🇨🇾",
    "flag_for_czech_republic": "🇨🇿",
    "flag_for_côte_d’ivoire": "🇨🇮",
    "flag_for_denmark": "🇩🇰",
    "flag_for_diego_garcia": "🇩🇬",
    "flag_for_djibouti": "🇩🇯",
    "flag_for_dominica": "🇩🇲",
    "flag_for_dominican_republic": "🇩🇴",
    "flag_for_ecuador": "🇪🇨",
    "flag_for_egypt": "🇪🇬",
    "flag_for_el_salvador": "🇸🇻",
    "flag_for_equatorial_guinea": "🇬🇶",
    "flag_for_eritrea": "🇪🇷",
    "flag_for_estonia": "🇪🇪",
    "flag_for_ethiopia": "🇪🇹",
    "flag_for_european_union": "🇪🇺",
    "flag_for_falkland_islands": "🇫🇰",
    "flag_for_faroe_islands": "🇫🇴",
    "flag_for_fiji": "🇫🇯",
    "flag_for_finland": "🇫🇮",
    "flag_for_france": "🇫🇷",
    "flag_for_french_guiana": "🇬🇫",
    "flag_for_french_polynesia": "🇵🇫",
    "flag_for_french_southern_territories": "🇹🇫",
    "flag_for_gabon": "🇬🇦",
    "flag_for_gambia": "🇬🇲",
    "flag_for_georgia": "🇬🇪",
    "flag_for_germany": "🇩🇪",
    "flag_for_ghana": "🇬🇭",
    "flag_for_gibraltar": "🇬🇮",
    "flag_for_greece": "🇬🇷",
    "flag_for_greenland": "🇬🇱",
    "flag_for_grenada": "🇬🇩",
    "flag_for_guadeloupe": "🇬🇵",
    "flag_for_guam": "🇬🇺",
    "flag_for_guatemala": "🇬🇹",
    "flag_for_guernsey": "🇬🇬",
    "flag_for_guinea": "🇬🇳",
    "flag_for_guinea__bissau": "🇬🇼",
    "flag_for_guyana": "🇬🇾",
    "flag_for_haiti": "🇭🇹",
    "flag_for_heard_&_mcdonald_islands": "🇭🇲",
    "flag_for_honduras": "🇭🇳",
    "flag_for_hong_kong": "🇭🇰",
    "flag_for_hungary": "🇭🇺",
    "flag_for_iceland": "🇮🇸",
    "flag_for_india": "🇮🇳",
    "flag_for_indonesia": "🇮🇩",
    "flag_for_iran": "🇮🇷",
    "flag_for_iraq": "🇮🇶",
    "flag_for_ireland": "🇮🇪",
    "flag_for_isle_of_man": "🇮🇲",
    "flag_for_israel": "🇮🇱",
    "flag_for_italy": "🇮🇹",
    "flag_for_jamaica": "🇯🇲",
    "flag_for_japan": "🇯🇵",
    "flag_for_jersey": "🇯🇪",
    "flag_for_jordan": "🇯🇴",
    "flag_for_kazakhstan": "🇰🇿",
    "flag_for_kenya": "🇰🇪",
    "flag_for_kiribati": "🇰🇮",
    "flag_for_kosovo": "🇽🇰",
    "flag_for_kuwait": "🇰🇼",
    "flag_for_kyrgyzstan": "🇰🇬",
    "flag_for_laos": "🇱🇦",
    "flag_for_latvia": "🇱🇻",
    "flag_for_lebanon": "🇱🇧",
    "flag_for_lesotho": "🇱🇸",
    "flag_for_liberia": "🇱🇷",
    "flag_for_libya": "🇱🇾",
    "flag_for_liechtenstein": "🇱🇮",
    "flag_for_lithuania": "🇱🇹",
    "flag_for_luxembourg": "🇱🇺",
    "flag_for_macau": "🇲🇴",
    "flag_for_macedonia": "🇲🇰",
    "flag_for_madagascar": "🇲🇬",
    "flag_for_malawi": "🇲🇼",
    "flag_for_malaysia": "🇲🇾",
    "flag_for_maldives": "🇲🇻",
    "flag_for_mali": "🇲🇱",
    "flag_for_malta": "🇲🇹",
    "flag_for_marshall_islands": "🇲🇭",
    "flag_for_martinique": "🇲🇶",
    "flag_for_mauritania": "🇲🇷",
    "flag_for_mauritius": "🇲🇺",
    "flag_for_mayotte": "🇾🇹",
    "flag_for_mexico": "🇲🇽",
    "flag_for_micronesia": "🇫🇲",
    "flag_for_moldova": "🇲🇩",
    "flag_for_monaco": "🇲🇨",
    "flag_for_mongolia": "🇲🇳",
    "flag_for_montenegro": "🇲🇪",
    "flag_for_montserrat": "🇲🇸",
    "flag_for_morocco": "🇲🇦",
    "flag_for_mozambique": "🇲🇿",
    "flag_for_myanmar": "🇲🇲",
    "flag_for_namibia": "🇳🇦",
    "flag_for_nauru": "🇳🇷",
    "flag_for_nepal": "🇳🇵",
    "flag_for_netherlands": "🇳🇱",
    "flag_for_new_caledonia": "🇳🇨",
    "flag_for_new_zealand": "🇳🇿",
    "flag_for_nicaragua": "🇳🇮",
    "flag_for_niger": "🇳🇪",
    "flag_for_nigeria": "🇳🇬",
    "flag_for_niue": "🇳🇺",
    "flag_for_norfolk_island": "🇳🇫",
    "flag_for_north_korea": "🇰🇵",
    "flag_for_northern_mariana_islands": "🇲🇵",
    "flag_for_norway": "🇳🇴",
    "flag_for_oman": "🇴🇲",
    "flag_for_pakistan": "🇵🇰",
    "flag_for_palau": "🇵🇼",
    "flag_for_palestinian_territories": "🇵🇸",
    "flag_for_panama": "🇵🇦",
    "flag_for_papua_new_guinea": "🇵🇬",
    "flag_for_paraguay": "🇵🇾",
    "flag_for_peru": "🇵🇪",
    "flag_for_philippines": "🇵🇭",
    "flag_for_pitcairn_islands": "🇵🇳",
    "flag_for_poland": "🇵🇱",
    "flag_for_portugal": "🇵🇹",
    "flag_for_puerto_rico": "🇵🇷",
    "flag_for_qatar": "🇶🇦",
    "flag_for_romania": "🇷🇴",
    "flag_for_russia": "🇷🇺",
    "flag_for_rwanda": "🇷🇼",
    "flag_for_réunion": "🇷🇪",
    "flag_for_samoa": "🇼🇸",
    "flag_for_san_marino": "🇸🇲",
    "flag_for_saudi_arabia": "🇸🇦",
    "flag_for_senegal": "🇸🇳",
    "flag_for_serbia": "🇷🇸",
    "flag_for_seychelles": "🇸🇨",
    "flag_for_sierra_leone": "🇸🇱",
    "flag_for_singapore": "🇸🇬",
    "flag_for_sint_maarten": "🇸🇽",
    "flag_for_slovakia": "🇸🇰",
    "flag_for_slovenia": "🇸🇮",
    "flag_for_solomon_islands": "🇸🇧",
    "flag_for_somalia": "🇸🇴",
    "flag_for_south_africa": "🇿🇦",
    "flag_for_south_georgia_&_south_sandwich_islands": "🇬🇸",
    "flag_for_south_korea": "🇰🇷",
    "flag_for_south_sudan": "🇸🇸",
    "flag_for_spain": "🇪🇸",
    "flag_for_sri_lanka": "🇱🇰",
    "flag_for_st._barthélemy": "🇧🇱",
    "flag_for_st._helena": "🇸🇭",
    "flag_for_st._kitts_&_nevis": "🇰🇳",
    "flag_for_st._lucia": "🇱🇨",
    "flag_for_st._martin": "🇲🇫",
    "flag_for_st._pierre_&_miquelon": "🇵🇲",
    "flag_for_st._vincent_&_grenadines": "🇻🇨",
    "flag_for_sudan": "🇸🇩",
    "flag_for_suriname": "🇸🇷",
    "flag_for_svalbard_&_jan_mayen": "🇸🇯",
    "flag_for_swaziland": "🇸🇿",
    "flag_for_sweden": "🇸🇪",
    "flag_for_switzerland": "🇨🇭",
    "flag_for_syria": "🇸🇾",
    "flag_for_são_tomé_&_príncipe": "🇸🇹",
    "flag_for_taiwan": "🇹🇼",
    "flag_for_tajikistan": "🇹🇯",
    "flag_for_tanzania": "🇹🇿",
    "flag_for_thailand": "🇹🇭",
    "flag_for_timor__leste": "🇹🇱",
    "flag_for_togo": "🇹🇬",
    "flag_for_tokelau": "🇹🇰",
    "flag_for_tonga": "🇹🇴",
    "flag_for_trinidad_&_tobago": "🇹🇹",
    "flag_for_tristan_da_cunha": "🇹🇦",
    "flag_for_tunisia": "🇹🇳",
    "flag_for_turkey": "🇹🇷",
    "flag_for_turkmenistan": "🇹🇲",
    "flag_for_turks_&_caicos_islands": "🇹🇨",
    "flag_for_tuvalu": "🇹🇻",
    "flag_for_u.s._outlying_islands": "🇺🇲",
    "flag_for_u.s._virgin_islands": "🇻🇮",
    "flag_for_uganda": "🇺🇬",
    "flag_for_ukraine": "🇺🇦",
    "flag_for_united_arab_emirates": "🇦🇪",
    "flag_for_united_kingdom": "🇬🇧",
    "flag_for_united_states": "🇺🇸",
    "flag_for_uruguay": "🇺🇾",
    "flag_for_uzbekistan": "🇺🇿",
    "flag_for_vanuatu": "🇻🇺",
    "flag_for_vatican_city": "🇻🇦",
    "flag_for_venezuela": "🇻🇪",
    "flag_for_vietnam": "🇻🇳",
    "flag_for_wallis_&_futuna": "🇼🇫",
    "flag_for_western_sahara": "🇪🇭",
    "flag_for_yemen": "🇾🇪",
    "flag_for_zambia": "🇿🇲",
    "flag_for_zimbabwe": "🇿🇼",
    "flag_for_åland_islands": "🇦🇽",
    "golf": "⛳",
    "fleur__de__lis": "⚜",
    "muscle": "💪",
    "flushed": "😳",
    "frame_with_picture": "🖼",
    "fries": "🍟",
    "frog": "🐸",
    "hatched_chick": "🐥",
    "frowning": "😦",
    "fuelpump": "⛽",
    "full_moon_with_face": "🌝",
    "gem": "💎",
    "star2": "🌟",
    "golfer": "🏌",
    "mortar_board": "🎓",
    "grimacing": "😬",
    "smile_cat": "😸",
    "grinning": "😀",
    "grin": "😁",
    "heartpulse": "💗",
    "guardsman": "💂",
    "haircut": "💇",
    "hamster": "🐹",
    "raising_hand": "🙋",
    "headphones": "🎧",
    "hear_no_evil": "🙉",
    "cupid": "💘",
    "gift_heart": "💝",
    "heart": "❤",
    "exclamation": "❗",
    "heavy_exclamation_mark": "❗",
    "heavy_heart_exclamation_mark_ornament": "❣",
    "o": "⭕",
    "helm_symbol": "⎈",
    "helmet_with_white_cross": "⛑",
    "high_heel": "👠",
    "bullettrain_side": "🚄",
    "bullettrain_front": "🚅",
    "high_brightness": "🔆",
    "zap": "⚡",
    "hocho": "🔪",
    "knife": "🔪",
    "bee": "🐝",
    "traffic_light": "🚥",
    "racehorse": "🐎",
    "coffee": "☕",
    "hotsprings": "♨",
    "hourglass": "⌛",
    "hourglass_flowing_sand": "⏳",
    "house_buildings": "🏘",
    "100": "💯",
    "hushed": "😯",
    "ice_hockey_stick_and_puck": "🏒",
    "imp": "👿",
    "information_desk_person": "💁",
    "information_source": "ℹ",
    "capital_abcd": "🔠",
    "abc": "🔤",
    "abcd": "🔡",
    "1234": "🔢",
    "symbols": "🔣",
    "izakaya_lantern": "🏮",
    "lantern": "🏮",
    "jack_o_lantern": "🎃",
    "dolls": "🎎",
    "japanese_goblin": "👺",
    "japanese_ogre": "👹",
    "beginner": "🔰",
    "zero": "0️⃣",
    "one": "1️⃣",
    "ten": "🔟",
    "two": "2️⃣",
    "three": "3️⃣",
    "four": "4️⃣",
    "five": "5️⃣",
    "six": "6️⃣",
    "seven": "7️⃣",
    "eight": "8️⃣",
    "nine": "9️⃣",
    "couplekiss": "💏",
    "kissing_cat": "😽",
    "kissing": "😗",
    "kissing_closed_eyes": "😚",
    "kissing_smiling_eyes": "😙",
    "beetle": "🐞",
    "large_blue_circle": "🔵",
    "last_quarter_moon_with_face": "🌜",
    "leaves": "🍃",
    "mag": "🔍",
    "left_right_arrow": "↔",
    "leftwards_arrow_with_hook": "↩",
    "arrow_left": "⬅",
    "lock": "🔒",
    "lock_with_ink_pen": "🔏",
    "sob": "😭",
    "low_brightness": "🔅",
    "lower_left_ballpoint_pen": "🖊",
    "lower_left_crayon": "🖍",
    "lower_left_fountain_pen": "🖋",
    "lower_left_paintbrush": "🖌",
    "mahjong": "🀄",
    "couple": "👫",
    "man_in_business_suit_levitating": "🕴",
    "man_with_gua_pi_mao": "👲",
    "man_with_turban": "👳",
    "mans_shoe": "👞",
    "shoe": "👞",
    "menorah_with_nine_branches": "🕎",
    "mens": "🚹",
    "minidisc": "💽",
    "iphone": "📱",
    "calling": "📲",
    "money__mouth_face": "🤑",
    "moneybag": "💰",
    "rice_scene": "🎑",
    "mountain_bicyclist": "🚵",
    "mouse2": "🐁",
    "lips": "👄",
    "moyai": "🗿",
    "notes": "🎶",
    "nail_care": "💅",
    "ab": "🆎",
    "negative_squared_cross_mark": "❎",
    "a": "🅰",
    "b": "🅱",
    "o2": "🅾",
    "parking": "🅿",
    "new_moon_with_face": "🌚",
    "no_entry_sign": "🚫",
    "underage": "🔞",
    "non__potable_water": "🚱",
    "arrow_upper_right": "↗",
    "arrow_upper_left": "↖",
    "office": "🏢",
    "older_man": "👴",
    "older_woman": "👵",
    "om_symbol": "🕉",
    "on": "🔛",
    "book": "📖",
    "unlock": "🔓",
    "mailbox_with_no_mail": "📭",
    "mailbox_with_mail": "📬",
    "cd": "💿",
    "tada": "🎉",
    "feet": "🐾",
    "walking": "🚶",
    "pencil2": "✏",
    "pensive": "😔",
    "persevere": "😣",
    "bow": "🙇",
    "raised_hands": "🙌",
    "person_with_ball": "⛹",
    "person_with_blond_hair": "👱",
    "pray": "🙏",
    "person_with_pouting_face": "🙎",
    "computer": "💻",
    "pig2": "🐖",
    "hankey": "💩",
    "poop": "💩",
    "shit": "💩",
    "bamboo": "🎍",
    "gun": "🔫",
    "black_joker": "🃏",
    "rotating_light": "🚨",
    "cop": "👮",
    "stew": "🍲",
    "pouch": "👝",
    "pouting_cat": "😾",
    "rage": "😡",
    "put_litter_in_its_place": "🚮",
    "rabbit2": "🐇",
    "racing_motorcycle": "🏍",
    "radioactive_sign": "☢",
    "fist": "✊",
    "hand": "✋",
    "raised_hand_with_fingers_splayed": "🖐",
    "raised_hand_with_part_between_middle_and_ring_fingers": "🖖",
    "blue_car": "🚙",
    "apple": "🍎",
    "relieved": "😌",
    "reversed_hand_with_middle_finger_extended": "🖕",
    "mag_right": "🔎",
    "arrow_right_hook": "↪",
    "sweet_potato": "🍠",
    "robot": "🤖",
    "rolled__up_newspaper": "🗞",
    "rowboat": "🚣",
    "runner": "🏃",
    "running": "🏃",
    "running_shirt_with_sash": "🎽",
    "boat": "⛵",
    "scales": "⚖",
    "school_satchel": "🎒",
    "scorpius": "♏",
    "see_no_evil": "🙈",
    "sheep": "🐑",
    "stars": "🌠",
    "cake": "🍰",
    "six_pointed_star": "🔯",
    "ski": "🎿",
    "sleeping_accommodation": "🛌",
    "sleeping": "😴",
    "sleepy": "😪",
    "sleuth_or_spy": "🕵",
    "heart_eyes_cat": "😻",
    "smiley_cat": "😺",
    "innocent": "😇",
    "heart_eyes": "😍",
    "smiling_imp": "😈",
    "smiley": "😃",
    "sweat_smile": "😅",
    "smile": "😄",
    "laughing": "😆",
    "satisfied": "😆",
    "blush": "😊",
    "smirk": "😏",
    "smoking": "🚬",
    "snow_capped_mountain": "🏔",
    "soccer": "⚽",
    "icecream": "🍦",
    "soon": "🔜",
    "arrow_lower_right": "↘",
    "arrow_lower_left": "↙",
    "speak_no_evil": "🙊",
    "speaker": "🔈",
    "mute": "🔇",
    "sound": "🔉",
    "loud_sound": "🔊",
    "speaking_head_in_silhouette": "🗣",
    "spiral_calendar_pad": "🗓",
    "spiral_note_pad": "🗒",
    "shell": "🐚",
    "sweat_drops": "💦",
    "u5272": "🈹",
    "u5408": "🈴",
    "u55b6": "🈺",
    "u6307": "🈯",
    "u6708": "🈷",
    "u6709": "🈶",
    "u6e80": "🈵",
    "u7121": "🈚",
    "u7533": "🈸",
    "u7981": "🈲",
    "u7a7a": "🈳",
    "cl": "🆑",
    "cool": "🆒",
    "free": "🆓",
    "id": "🆔",
    "koko": "🈁",
    "sa": "🈂",
    "new": "🆕",
    "ng": "🆖",
    "ok": "🆗",
    "sos": "🆘",
    "up": "🆙",
    "vs": "🆚",
    "steam_locomotive": "🚂",
    "ramen": "🍜",
    "partly_sunny": "⛅",
    "city_sunrise": "🌇",
    "surfer": "🏄",
    "swimmer": "🏊",
    "shirt": "👕",
    "tshirt": "👕",
    "table_tennis_paddle_and_ball": "🏓",
    "tea": "🍵",
    "tv": "📺",
    "three_button_mouse": "🖱",
    "+1": "👍",
    "thumbsup": "👍",
    "__1": "👎",
    "-1": "👎",
    "thumbsdown": "👎",
    "thunder_cloud_and_rain": "⛈",
    "tiger2": "🐅",
    "tophat": "🎩",
    "top": "🔝",
    "tm": "™",
    "train2": "🚆",
    "triangular_flag_on_post": "🚩",
    "trident": "🔱",
    "twisted_rightwards_arrows": "🔀",
    "unamused": "😒",
    "small_red_triangle": "🔺",
    "arrow_up_small": "🔼",
    "arrow_up_down": "↕",
    "upside__down_face": "🙃",
    "arrow_up": "⬆",
    "v": "✌",
    "vhs": "📼",
    "wc": "🚾",
    "ocean": "🌊",
    "waving_black_flag": "🏴",
    "wave": "👋",
    "waving_white_flag": "🏳",
    "moon": "🌔",
    "scream_cat": "🙀",
    "weary": "😩",
    "weight_lifter": "🏋",
    "whale2": "🐋",
    "wheelchair": "♿",
    "point_down": "👇",
    "grey_exclamation": "❕",
    "white_frowning_face": "☹",
    "white_check_mark": "✅",
    "point_left": "👈",
    "white_medium_small_square": "◽",
    "star": "⭐",
    "grey_question": "❔",
    "point_right": "👉",
    "relaxed": "☺",
    "white_sun_behind_cloud": "🌥",
    "white_sun_behind_cloud_with_rain": "🌦",
    "white_sun_with_small_cloud": "🌤",
    "point_up_2": "👆",
    "point_up": "☝",
    "wind_blowing_face": "🌬",
    "wink": "😉",
    "wolf": "🐺",
    "dancers": "👯",
    "boot": "👢",
    "womans_clothes": "👚",
    "womans_hat": "👒",
    "sandal": "👡",
    "womens": "🚺",
    "worried": "😟",
    "gift": "🎁",
    "zipper__mouth_face": "🤐",
    "regional_indicator_a": "🇦",
    "regional_indicator_b": "🇧",
    "regional_indicator_c": "🇨",
    "regional_indicator_d": "🇩",
    "regional_indicator_e": "🇪",
    "regional_indicator_f": "🇫",
    "regional_indicator_g": "🇬",
    "regional_indicator_h": "🇭",
    "regional_indicator_i": "🇮",
    "regional_indicator_j": "🇯",
    "regional_indicator_k": "🇰",
    "regional_indicator_l": "🇱",
    "regional_indicator_m": "🇲",
    "regional_indicator_n": "🇳",
    "regional_indicator_o": "🇴",
    "regional_indicator_p": "🇵",
    "regional_indicator_q": "🇶",
    "regional_indicator_r": "🇷",
    "regional_indicator_s": "🇸",
    "regional_indicator_t": "🇹",
    "regional_indicator_u": "🇺",
    "regional_indicator_v": "🇻",
    "regional_indicator_w": "🇼",
    "regional_indicator_x": "🇽",
    "regional_indicator_y": "🇾",
    "regional_indicator_z": "🇿",
}
