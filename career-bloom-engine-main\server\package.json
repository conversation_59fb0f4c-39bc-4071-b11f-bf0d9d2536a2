{"name": "career-bloom-server", "version": "1.0.0", "description": "Career Bloom Backend Server", "main": "index.ts", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only index.ts", "build": "tsc", "seed-jobs": "ts-node scripts/seedJobs.ts", "populate-jobs": "ts-node scripts/populateJobs.ts", "populate-jobs-quick": "ts-node scripts/quickPopulate.ts", "add-more-jobs": "ts-node scripts/addMoreJobs.ts", "init-chatbot": "ts-node scripts/initializeChatbot.ts", "test-llm": "ts-node test-llm-chatbot.ts", "test-llm-only": "cross-env SKIP_DB=true ts-node test-llm-chatbot.ts", "test-hybrid": "node test-hybrid-chat.js", "test-simple": "node test-simple.js"}, "dependencies": {"@clerk/clerk-sdk-node": "^4.13.23", "@google/generative-ai": "^0.24.1", "@huggingface/inference": "^3.13.1", "@types/dotenv": "^6.1.1", "@types/node-cron": "^3.0.11", "@xenova/transformers": "^2.17.2", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0", "chromadb": "^2.4.6", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.26", "mammoth": "^1.9.1", "mongodb": "^6.3.0", "mongoose": "^7.5.3", "multer": "^1.4.5-lts.1", "node-cron": "^4.0.5", "node-fetch": "^2.7.0", "onnxruntime-node": "^1.17.0", "onnxruntime-web": "^1.17.0", "openai": "^4.35.0", "pdf-parse": "^1.1.1", "pdf2pic": "^3.2.0", "pdfjs-dist": "^4.8.69", "tesseract.js": "^6.0.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cheerio": "^0.22.35", "@types/cors": "^2.8.18", "@types/express": "^4.17.22", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.96", "@types/multer": "^2.0.0", "@types/node": "^18.19.105", "@types/node-fetch": "^2.6.12", "@types/pdf-parse": "^1.1.5", "@types/pdfkit": "^0.14.0", "jest": "^29.7.0", "nodemon": "^3.0.3", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}