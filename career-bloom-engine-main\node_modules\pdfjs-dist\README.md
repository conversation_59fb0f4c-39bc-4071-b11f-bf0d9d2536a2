# PDF.js

PDF.js is a Portable Document Format (PDF) library that is built with HTML5.
Our goal is to create a general-purpose, web standards-based platform for
parsing and rendering PDFs.

This is a pre-built version of the PDF.js source code. It is automatically
generated by the build scripts.

For usage with older browsers/environments, without native support for the
latest JavaScript features, please see the `legacy/` folder.
Please see [this wiki page](https://github.com/mozilla/pdf.js/wiki/Frequently-Asked-Questions#faq-support) for information about supported browsers/environments.

See https://github.com/mozilla/pdf.js for learning and contributing.
