# Dependencies
node_modules/
server/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
server/.env

# Build outputs
dist/
dist-ssr/
build/
server/dist/

# Logs
logs
*.log

# Uploads and temporary files
uploads/*.pdf
uploads/*.doc
uploads/*.docx
uploads/*.jpeg
uploads/*.jpg
uploads/*.png
server/uploads/
test-*.pdf
test-*.txt
test-*.js

# Python cache
__pycache__/
*.py[cod]
*$py.class

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.local

# Cache
.cache/
.parcel-cache/
.eslintcache

# Database files
*.db
*.sqlite
*.sqlite3

# TypeScript
*.tsbuildinfo
