@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  /* Prevent horizontal overflow */
  .prevent-overflow {
    max-width: 100%;
    overflow-x: hidden;
  }

  /* Job card specific utilities */
  .job-card-container {
    max-width: 100%;
    overflow: hidden;
  }

  .job-title-text {
    word-break: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }
}

@layer base {
  :root {
    --background: 240 50% 99%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 262 80% 75%;
    --primary-foreground: 210 40% 98%;

    --secondary: 260 29% 54%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 260 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 262 80% 75%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 262 80% 70%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 260 29% 54%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262.1 83.3% 57.8%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .btn-gradient {
    @apply bg-gradient-to-br from-primary-purple to-secondary-purple text-white hover:opacity-90 transition-opacity;
  }
  
  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }
  
  .purple-gradient {
    @apply bg-gradient-to-r from-primary-purple to-secondary-purple;
  }
}

/* Clerk styling overrides */
.clerk-component {
  --clerk-primary: hsl(var(--primary));
  --clerk-primary-hover: hsl(var(--primary) / 0.9);
  --clerk-text-color: hsl(var(--foreground));
  --clerk-background-color: hsl(var(--background));
  --clerk-border-color: hsl(var(--border));
  
  color: hsl(var(--foreground));
  padding: 0;
}

.clerk-component .cl-card, 
.clerk-component .cl-socialButtonsIconButton,
.clerk-component .cl-formButtonPrimary,
.clerk-component .cl-formButtonReset,
.clerk-component .cl-footerActionLink {
  color: hsl(var(--foreground));
}

.clerk-component .cl-formFieldInput {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border-color: hsl(var(--border));
}

.clerk-component .cl-formFieldLabel {
  color: hsl(var(--foreground));
}

.clerk-component .cl-headerTitle,
.clerk-component .cl-headerSubtitle {
  color: hsl(var(--foreground));
}

.clerk-component .cl-dividerText {
  color: hsl(var(--muted-foreground));
}

.clerk-component .cl-alert {
  background-color: hsl(var(--background));
  border-color: hsl(var(--border));
  color: hsl(var(--foreground));
}
