{"name": "pdf-parse", "version": "1.1.1", "description": "Pure javascript cross-platform module to extract text from PDFs.", "main": "index.js", "keywords": ["pdf-parse", "pdf-crawler", "xpdf", "pdf.js", "pdfreader", "pdf-extractor", "pdf2json", "j-pd<PERSON><PERSON><PERSON>", "pdf-parser", "pdf-extract", "pdf-extractor", "pdf-to-text", "pdf-text-extract", "pdfjs", "server side PDF parsing", "pdf metadata"], "dependencies": {"debug": "^3.1.0", "node-ensure": "^0.0.0"}, "devDependencies": {"mocha": "^4.0.1"}, "scripts": {"test": "node node_modules/mocha/bin/_mocha --recursive --slow 10000", "start": "node index.js"}, "homepage": "https://gitlab.com/autokent/pdf-parse", "bugs": {"url": "https://gitlab.com/autokent/pdf-parse/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://gitlab.com/autokent/pdf-parse.git"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "engines": {"node": ">=6.8.1"}}